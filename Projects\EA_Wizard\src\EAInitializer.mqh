#property strict

#include "OnInitStage/OnInitStage.mqh"
#include "../module/Stage/StageRegistry.mqh"

ENUM_INIT_RETCODE EAInitializer(){
   Print("EA初始化開始");
   Print("初始化階段註冊器註冊: ", StageManagerRegistry<IPipelineManager<ENUM_INIT_RETCODE, void*>*>::IsActivated());
   Print("初始化流水線註冊器註冊: ", StageRegistry::IsActivated());
   Print("初始化流水線管理器註冊: ", OnInitStageManager::IsRegistered());

   // 檢查初始化階段註冊器是否已註冊
   if(!StageManagerRegistry<IPipelineManager<ENUM_INIT_RETCODE, void*>*>::IsActivated()) {
       Print("警告: 初始化階段註冊器未註冊，EA初始化可能失敗");
       return INIT_FAILED;
   }

   // 檢查初始化流水線註冊器是否已註冊
   if(!StageRegistry::IsActivated()) {
       Print("警告: 初始化流水線註冊器未註冊，EA初始化可能失敗");
       return INIT_FAILED;
   }

   // 檢查初始化流水線管理器是否已註冊
   if(!OnInitStageManager::IsRegistered()) {
       Print("警告: 初始化流水線管理器未註冊，EA初始化可能失敗");
       return INIT_FAILED;
   }

    // 創建並執行初始化流水線處理器
    OnInitStageManager* processor = OnInitStageManager::GetInstance();
    ENUM_INIT_RETCODE result = processor.Execute();

    // 處理初始化結果
    switch (result)
    {
    case INIT_PARAMETERS_INCORRECT:
       Print("EA初始化失敗，原因: 參數錯誤");
       delete processor;
       return(result);
    case INIT_FAILED:
       Print("EA初始化失敗，原因: 初始化失敗");
       delete processor;
       return(result);
    }

    Print("EA初始化成功");
    delete processor;
    return(INIT_SUCCEEDED);
}
