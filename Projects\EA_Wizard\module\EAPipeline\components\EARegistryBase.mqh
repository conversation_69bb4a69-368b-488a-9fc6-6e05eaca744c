#property strict

#include "../../RegistryAdvance/interface/IRegistry.mqh"
#include "../../RegistryAdvance/ItemRegistry.mqh"

#define EA_REGISTRY_BASE_NAME "EARegistryBase"
#define EA_REGISTRY_BASE_TYPE "EARegistryBase"
#define EA_REGISTRY_BASE_MAX_ITEMS 100

//+------------------------------------------------------------------+
//| EARegistryBase 類 - 裝飾者模式實現                               |
//+------------------------------------------------------------------+
class EARegistryBase : public IRegistry<string, void*>
{
private:
    ItemRegistry<void*>* m_wrappee;  // 被裝飾的註冊器
    string m_name;                   // 註冊器名稱
    string m_type;                   // 註冊器類型
    int m_max_items;                 // 最大項目數量

public:
    // 構造函數
    EARegistryBase(ItemRegistry<void*>* registry, string type = EA_REGISTRY_BASE_TYPE)
        : m_wrappee(registry),
          m_name(registry != NULL ? registry.GetName() : ""),
          m_type(type),
          m_max_items(registry != NULL ? registry.GetMaxItems() : 0)
    {
    }

    EARegistryBase(EARegistryBase* registry, string type = EA_REGISTRY_BASE_TYPE)
        : m_wrappee(NULL),
          m_name(registry.GetName()),
          m_type(type),
          m_max_items(0)
    {
        m_wrappee = registry.GetRegistry();
        m_max_items = registry.GetMaxItems();
    }
    
    EARegistryBase(string name, string type = EA_REGISTRY_BASE_TYPE, int maxItems = EA_REGISTRY_BASE_MAX_ITEMS)
        : m_wrappee(new ItemRegistry<void*>(name, type, maxItems)), 
          m_name(name), 
          m_type(type),
          m_max_items(maxItems)
    {
    }

    // 析構函數
    virtual ~EARegistryBase()
    {
        // 注意：不刪除被裝飾的註冊器，因為它可能被其他對象引用
    }

    // 註冊新項目
    virtual RegistryResult<string>* Register(const string name, const string description, void* value) override
    {
        if(m_wrappee == NULL)
        {
            return new RegistryResult<string>(false, "被裝飾的註冊器為空", "", m_name);
        }
        return m_wrappee.Register(name, description, value);
    }

    // 移除項目
    virtual bool Unregister(const string key) override
    {
        if(m_wrappee == NULL)
        {
            return false;
        }
        return m_wrappee.Unregister(key);
    }

    // 清空註冊器
    virtual void Clear() override
    {
        if(m_wrappee != NULL)
        {
            m_wrappee.Clear();
        }
    }

    // 獲取註冊器名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取項目數量
    virtual int GetCount() override
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_wrappee.GetCount();
    }

    // 獲取最大項目數量
    virtual int GetMaxItems() override
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_max_items;
    }

    // 根據Key獲取項目
    virtual RegistryItem<void*>* GetItem(const string key) override
    {
        if(m_wrappee == NULL)
        {
            return NULL;
        }
        return m_wrappee.GetItem(key);
    }

    // 獲取所有Key
    virtual int GetAllKeys(string &Keys[]) override
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_wrappee.GetAllKeys(Keys);
    }

    // 獲取最後註冊的鍵
    virtual string GetLastRegisteredKey() override
    {
        if(m_wrappee == NULL)
        {
            return "";
        }
        return m_wrappee.GetLastRegisteredKey();
    }

    // 獲取被裝飾的註冊器
    virtual ItemRegistry<void*>* GetRegistry()
    {
        return m_wrappee;
    }

protected:
    // 設置被裝飾的註冊器
    virtual void SetRegistry(ItemRegistry<void*>* registry)
    {
        m_wrappee = registry;
        m_max_items = registry.GetMaxItems();
    }
};
