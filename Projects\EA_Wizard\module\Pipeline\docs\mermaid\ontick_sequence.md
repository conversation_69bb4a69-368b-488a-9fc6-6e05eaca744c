```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as TickPipelineProcessor
    participant Stage1 as OnTickStartStage
    participant Stage2 as MarketDataStage
    participant Stage3 as PositionCheckStage
    participant Stage4 as SignalCheckStage
    participant Stage5 as RiskCheckStage
    participant Stage6 as TradeExecutionStage
    participant Stage7 as OnTickEndStage

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 OnTickStartStage
    Processor->>Stage2: 添加 MarketDataStage
    Processor->>Stage3: 添加 PositionCheckStage
    Processor->>Stage4: 添加 SignalCheckStage
    Processor->>Stage5: 添加 RiskCheckStage
    Processor->>Stage6: 添加 TradeExecutionStage
    Processor->>Stage7: 添加 OnTickEndStage
    EA->>Processor: Execute()
    Processor->>Stage1: Execute(NULL)
    Stage1-->>Processor: 返回 true
    Processor->>Stage2: Execute(NULL)
    Stage2-->>Processor: 返回 true
    Processor->>Stage3: Execute(NULL)
    Stage3-->>Processor: 返回 true
    Processor->>Stage4: Execute(NULL)
    Stage4-->>Processor: 返回 true
    Processor->>Stage5: Execute(NULL)
    Stage5-->>Processor: 返回 true
    Processor->>Stage6: Execute(NULL)
    Stage6-->>Processor: 返回 true
    Processor->>Stage7: Execute(NULL)
    Stage7-->>Processor: 返回 true
    Processor-->>EA: 返回 true
    EA->>Processor: 釋放資源
```
