#property strict

//+------------------------------------------------------------------+
//| Error<PERSON><PERSON>ler class definition                                    |
//+------------------------------------------------------------------+

// 防止重複包含
#ifndef _ERROR_HANDLER_MQH_
#define _ERROR_HANDLER_MQH_

#define DEFAULT_MAX_ERRORS 10

class ErrorHandler
  {
private:
   string   m_last_error;
   string   m_error_log[];
   int      m_error_count;
   int      m_max_errors;
   bool     m_is_error;
   bool     m_printable;

public:
    // Constructor
    ErrorHandler(int maxErrors = DEFAULT_MAX_ERRORS, bool printable = true)
    {
        m_last_error = "";
        m_error_count = 0;
        m_max_errors = maxErrors;
        m_is_error = false;
        m_printable = printable;
        ArrayResize(m_error_log, m_max_errors);
    }
    
    void SetMaxErrors(int maxErr)
    {
        if(maxErr > 0)
        {
            m_max_errors = maxErr;
            ArrayResize(m_error_log, m_max_errors);
            if(m_error_count > m_max_errors)
            m_error_count = m_max_errors;
        }
    }

   void HandleError(string error)
     {
      m_last_error = error;
      m_is_error = true;
      if(m_error_count < m_max_errors)
      {
        m_error_log[m_error_count] = error;
        m_error_count++;
      }
      else
      {
       // Shift all entries left by one to make room for the new error
       for(int i = 1; i < m_error_count; i++)
        m_error_log[i-1] = m_error_log[i];
        m_error_log[m_max_errors - 1] = error; // Add new error at the end
      }
      LogError(error);
     }

   void LogError(string error)
     {
      if(m_printable) {
        Print("Error logged: ", error);
      }
     }

   void ClearErrors()
     {
      ArrayFree(m_error_log);
      ArrayResize(m_error_log, m_max_errors);
      m_error_count = 0;
      m_last_error = "";
      m_is_error = false;
     }

   string GetLastError()
     {
      return m_last_error;
     }

   int GetErrorCount()
     {
      return m_error_count;
     }

   bool IsError()
     {
      return m_is_error;
     }

   bool IsPrintable()
     {
      return m_printable;
     }

   void SetPrintable(bool printable)
     {
      m_printable = printable;
     }

   // 獲取所有錯誤
   int GetAllErrors(string &errors[])
     {
      ArrayResize(errors, m_error_count);
      for(int i = 0; i < m_error_count; i++)
        {
         errors[i] = m_error_log[i];
        }
      return m_error_count;
     }

   // 獲取所有錯誤為字串
   string GetAllErrorsAsString(string separator = "\n")
     {
      string result = "";
      for(int i = 0; i < m_error_count; i++)
        {
         result += m_error_log[i];
         if(i < m_error_count - 1)
           result += separator;
        }
      return result;
     }
  };

// 結束防止重複包含
#endif _ERROR_HANDLER_MQH_