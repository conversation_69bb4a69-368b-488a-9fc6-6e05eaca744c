#property strict

#include "../../module/Stage/OnInitStage.mqh"
#include "InitCheck.mqh"
#include "InitErrorHandler.mqh"
#include "InitOrderTracker.mqh"
#include "InitOrderManager.mqh"
#include "InitMqlWrappers.mqh"
#include "InitSignalHandler.mqh"

// #include "../../module/Pipeline/OnInitPipeline.mqh"
// #include "OnInitStartStage.mqh"
// #include "ParameterReadStage.mqh"
// #include "VariableInitStage.mqh"
// #include "TradingEnvironmentCheckStage.mqh"
// #include "IndicatorInitStage.mqh"
// #include "OnInitEndStage.mqh"

// // OnInit 流水線處理器
// class InitPipelineProcessor : public OnInitPipelineManager
// {
// public:
//     InitPipelineProcessor(string symbol = NULL) {
//         // 設置註冊器的鍵字頭
//         GetRegistry().SetKeyPrefix("Init");

//         // 交易品種
//         string trade_symbol = (symbol == NULL) ? Symbol() : symbol;

//         // 添加 OnInit 開始階段
//         AddPipeline(new OnInitStartStage());

//         // 添加參數讀取階段
//         AddPipeline(new ParameterReadStage());

//         // 添加變數初始化階段
//         AddPipeline(new VariableInitStage(trade_symbol));

//         // 添加交易環境檢查階段
//         TradingEnvironmentCheckStage* envCheck = new TradingEnvironmentCheckStage();
//         AddPipeline(envCheck);

//         // 添加指標初始化階段
//         AddPipeline(new IndicatorInitStage(trade_symbol));

//         // 添加 OnInit 結束階段
//         AddPipeline(new OnInitEndStage());
//     }
// };
