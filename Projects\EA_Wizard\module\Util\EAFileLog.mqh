//+------------------------------------------------------------------+
//|                                                 FileLogDriver.mqh |
//|                                                        EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../MQL4Logger/FileLog.mqh"

// 日誌記錄器常量
#ifdef _DEBUG
// 調試模式下的日誌設置
#define DEFAULT_LOG_FILE_NAME "EA_Wizard_Debug.log"
#define DEFAULT_LOG_LEVEL DEBUG
#define DEFAULT_PRINT_TO_CONSOLE true
#define DEFAULT_APPEND_TO_EXISTING false
#else
// 正常模式下的日誌設置
#define DEFAULT_LOG_FILE_NAME "EA_Wizard.log"
#define DEFAULT_LOG_LEVEL INFO
#define DEFAULT_PRINT_TO_CONSOLE true
#define DEFAULT_APPEND_TO_EXISTING true
#endif

//+------------------------------------------------------------------+
//| EAFileLog 類別                                                   |
//| 繼承自 CFileLog，實現單例模式                                     |
//+------------------------------------------------------------------+
class EAFileLog : public CFileLog
{
private:
   static EAFileLog* s_instance;

   // 私有建構子，確保只能通過 GetInstance 方法獲取實例
   EAFileLog()
      : CFileLog(NULL)
   {
   }

public:
   // 獲取單例實例
   static EAFileLog* GetInstance()
   {
      if(s_instance == NULL)
      {
         s_instance = new EAFileLog();
         if(DEFAULT_APPEND_TO_EXISTING)
            s_instance.Open(DEFAULT_LOG_FILE_NAME, FILE_READ|FILE_WRITE);
         else
            s_instance.Open(DEFAULT_LOG_FILE_NAME, FILE_WRITE);
         s_instance.Seek(0, SEEK_END);
         s_instance.SetLevel(DEFAULT_LOG_LEVEL);
         s_instance.SetPrint(DEFAULT_PRINT_TO_CONSOLE);
         s_instance.Flush();
      }
      return s_instance;
   }

   // 釋放單例實例
   static void ReleaseInstance()
   {
      if(s_instance != NULL)
      {
         delete s_instance;
         s_instance = NULL;
      }
   }
};

// 初始化靜態實例指針
EAFileLog* EAFileLog::s_instance = NULL;
