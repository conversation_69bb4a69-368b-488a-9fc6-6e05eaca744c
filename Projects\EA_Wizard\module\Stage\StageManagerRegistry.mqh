//+------------------------------------------------------------------+
//|                                               StageManagerRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../Registry/StringRegistry.mqh"

//+------------------------------------------------------------------+
//| 階段管理器註冊器類 - 用於註冊和管理階段管理器                    |
//+------------------------------------------------------------------+
template <typename Val>
class StageManagerRegistry : public StringRegistry<Val>
{
private:
    static StageManagerRegistry<Val>* s_instance;           // 單例實例
    static bool s_isActivated;                        // 是否激活（靜態成員）

    // 私有構造函數（單例模式）
    StageManagerRegistry()
        : StringRegistry<Val>("StageManagerRegistry", 100)
    {
        this.SetKeyPrefix("SMR"); // 設置階段管理器註冊器的字頭
        s_isActivated = true;
    }

public:
    // 獲取單例實例
    static StageManagerRegistry<Val>* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new StageManagerRegistry<Val>();
        }
        return s_instance;
    }

    ~StageManagerRegistry() {
        s_isActivated = false;
    }

    // 是否激活
    static bool IsActivated() {
        return s_isActivated;
    }
};

// 靜態單例實例初始化為 NULL
template <typename Val>
StageManagerRegistry<Val>* StageManagerRegistry::s_instance = NULL;

template <typename Val>
bool StageManagerRegistry::s_isActivated = false;
