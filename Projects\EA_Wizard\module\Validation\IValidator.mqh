#property strict

#include "ValidationConfig.mqh"
#include "ValidationResult.mqh"

//+------------------------------------------------------------------+
//| 驗證器接口                                                         |
//| 定義所有驗證器必須實現的方法                                         |
//+------------------------------------------------------------------+
interface IValidator
{
public:
    // 獲取驗證器名稱
    string GetName();
    
    // 執行驗證並返回結果
    CValidationResult* Validate();
    
    // 檢查驗證器是否啟用
    bool IsEnabled() const;
    
    // 設置驗證器啟用狀態
    void SetEnabled(bool enabled);
    
    // 獲取驗證器類型
    int GetType() const;
};

//+------------------------------------------------------------------+
//| 驗證器基類                                                         |
//| 實現驗證器接口的基本功能                                             |
//+------------------------------------------------------------------+
class CBaseValidator : public IValidator
{
private:
    string m_name;         // 驗證器名稱
    bool   m_isEnabled;    // 啟用狀態
    int    m_type;         // 驗證器類型

protected:
    // 建構函數
    CBaseValidator(const string name, const int type = VALIDATOR_TYPE_CUSTOM)
        : m_name(name)
        , m_isEnabled(VALIDATOR_STATUS_ENABLED)
        , m_type(type)
    {}

public:
    // 獲取驗證器名稱
    virtual string GetName() override { return m_name; }
    
    // 檢查驗證器是否啟用
    virtual bool IsEnabled() const override { return m_isEnabled; }
    
    // 設置驗證器啟用狀態
    virtual void SetEnabled(bool enabled) override { m_isEnabled = enabled; }
    
    // 獲取驗證器類型
    virtual int GetType() const override { return m_type; }
    
    // 執行驗證（由子類實現）
    virtual CValidationResult* Validate() override = 0;
};
