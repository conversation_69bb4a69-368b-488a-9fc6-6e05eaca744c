# Pipeline 模組類別圖

```mermaid
classDiagram
    %% 基本介面和類別
    class IPipeline~Output, Input~ {
        <<interface>>
        +Execute(Input in) Output
    }

    class PipelineGroupBase~Output, Input~ {
        -m_pipelines[] IPipeline~Output, Input~*
        +PipelineGroupBase()
        +PipelineGroupBase(pipelines[] IPipeline~Output, Input~*)
        +~PipelineGroupBase()
        +HasPipeline(pipeline IPipeline~Output, Input~*) bool
        +AddPipeline(pipeline IPipeline~Output, Input~*) bool
        +RemovePipeline(pipeline IPipeline~Output, Input~*) bool
        +GetPipelines(pipelines[] IPipeline~Output, Input~*) int
        +Execute(Input in) Output
    }

    %% 管理器類別
    class PipelineManager~Output, Input~ {
        -m_name string
        -m_stopOnError bool
        -m_registry Registry~string, PipelineManager~Output, Input~*~*
        -m_id string
        +PipelineManager(name string, stopOnError bool)
        +~PipelineManager()
        +GetName() string
        +GetId() string
        +GetRegistry() Registry~string, PipelineManager~Output, Input~*~*
        +Execute(Input in) Output
        #IsError(Output result) bool
    }

    %% 可註冊流水線
    class RegistrablePipeline~Output, Input, Key, Val~ {
        -m_registry Registry~Key, Val~*
        +RegistrablePipeline(registry Registry~Key, Val~*)
        +Register(name string, description string, value Val, key Key) bool
        +GetRegistry() Registry~Key, Val~*
    }

    %% 特定流水線類型
    class OnInitPipeline {
        +Execute(void* in) ENUM_INIT_RETCODE
    }

    class OnTickPipeline {
        +Execute(void* in) bool
    }

    class OnDeinitPipeline {
        +Execute(int in) bool
    }

    %% 特定流水線管理器
    class OnInitPipelineManager {
        +OnInitPipelineManager()
        +Execute(void* in) ENUM_INIT_RETCODE
        #IsError(ENUM_INIT_RETCODE result) bool
    }

    class OnTickPipelineManager {
        +OnTickPipelineManager()
        +Execute(void* in) bool
        #IsError(bool result) bool
    }

    class OnDeinitPipelineManager {
        +OnDeinitPipelineManager()
        +Execute(int in) bool
        #IsError(bool result) bool
    }

    %% 範例階段
    class SampleStage {
        +Execute(int in) bool
    }

    %% 關係
    IPipeline <|.. PipelineGroupBase
    PipelineGroupBase <|-- PipelineManager
    IPipeline <|.. RegistrablePipeline
    IPipeline <|.. SampleStage
    
    IPipeline <|.. OnInitPipeline
    IPipeline <|.. OnTickPipeline
    IPipeline <|.. OnDeinitPipeline
    
    PipelineManager <|-- OnInitPipelineManager
    PipelineManager <|-- OnTickPipelineManager
    PipelineManager <|-- OnDeinitPipelineManager
```
