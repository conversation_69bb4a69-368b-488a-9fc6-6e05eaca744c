#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// 信號類型枚舉
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = 0,    // 無信號
    SIGNAL_BUY = 1,     // 買入信號
    SIGNAL_SELL = -1    // 賣出信號
};

// 信號生成階段
class SignalGenerationStage : public OnTickPipeline
{
private:
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;
    int m_signal_type;
    double m_signal_strength;
    
public:
    // 建構函數
    SignalGenerationStage(string symbol = NULL, ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT)
        : m_symbol(symbol == NULL ? Symbol() : symbol),
          m_timeframe(timeframe == PERIOD_CURRENT ? Period() : timeframe),
          m_signal_type(SIGNAL_NONE),
          m_signal_strength(0.0)
    {
    }
    
    // 獲取信號類型
    int GetSignalType() const
    {
        return m_signal_type;
    }
    
    // 獲取信號強度
    double GetSignalStrength() const
    {
        return m_signal_strength;
    }
    
    bool Execute(void* in = NULL) override
    {
        // 在這裡實現信號生成邏輯
        // 這只是一個示例，實際應用中應該根據具體的交易策略來生成信號
        
        // 示例: 使用移動平均線交叉策略
        double ma_fast[], ma_slow[];
        ArraySetAsSeries(ma_fast, true);
        ArraySetAsSeries(ma_slow, true);
        
        // 計算快速和慢速移動平均線
        int fast_period = 10;
        int slow_period = 20;
        int copied_fast = CopyBuffer(iMA(m_symbol, m_timeframe, fast_period, 0, MODE_SMA, PRICE_CLOSE), 0, 0, 3, ma_fast);
        int copied_slow = CopyBuffer(iMA(m_symbol, m_timeframe, slow_period, 0, MODE_SMA, PRICE_CLOSE), 0, 0, 3, ma_slow);
        
        if(copied_fast <= 0 || copied_slow <= 0)
        {
            Print("無法獲取移動平均線數據");
            return false;
        }
        
        // 檢查交叉信號
        bool fast_above_slow_current = ma_fast[0] > ma_slow[0];
        bool fast_above_slow_previous = ma_fast[1] > ma_slow[1];
        
        // 生成信號
        m_signal_type = SIGNAL_NONE;
        m_signal_strength = 0.0;
        
        // 快線從下方穿過慢線 = 買入信號
        if(!fast_above_slow_previous && fast_above_slow_current)
        {
            m_signal_type = SIGNAL_BUY;
            m_signal_strength = MathAbs(ma_fast[0] - ma_slow[0]) / ma_slow[0] * 100; // 計算信號強度
        }
        // 快線從上方穿過慢線 = 賣出信號
        else if(fast_above_slow_previous && !fast_above_slow_current)
        {
            m_signal_type = SIGNAL_SELL;
            m_signal_strength = MathAbs(ma_fast[0] - ma_slow[0]) / ma_slow[0] * 100; // 計算信號強度
        }
        
        // 輸出信號生成結果
        string signal_str = "";
        switch(m_signal_type)
        {
            case SIGNAL_BUY:  signal_str = "買入"; break;
            case SIGNAL_SELL: signal_str = "賣出"; break;
            default:          signal_str = "無";   break;
        }
        
        Print("信號生成結果 - 交易品種: ", m_symbol, 
              ", 時間週期: ", EnumToString(m_timeframe),
              ", 信號類型: ", signal_str,
              ", 信號強度: ", DoubleToString(m_signal_strength, 2), "%");
        
        return true;
    }
};
