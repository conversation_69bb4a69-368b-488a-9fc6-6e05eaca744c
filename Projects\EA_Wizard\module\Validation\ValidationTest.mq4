//+------------------------------------------------------------------+
//|                                                ValidationTest.mq4 |
//|                                                       EA_Wizard   |
//|                                                                   |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "ValidationExample.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
   // 執行所有驗證示例
   Print("===== 開始測試驗證模組 =====");
   CValidationExample::RunAllExamples();
   Print("===== 驗證模組測試完成 =====");
}
//+------------------------------------------------------------------+
