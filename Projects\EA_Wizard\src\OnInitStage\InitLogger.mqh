#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/Util/ErrorHandler.mqh"
#include "../../module/MQL4Logger/FileLog.mqh"

// 日誌記錄器常量
const string DEFAULT_LOG_FILE_NAME = "EA_Wizard.log";
const ENUM_LOG_LEVEL DEFAULT_LOG_LEVEL = INFO;
const bool DEFAULT_PRINT_TO_CONSOLE = true;
const bool DEFAULT_APPEND_TO_EXISTING = true;

// 初始化日誌記錄器階段
class InitLogger : public OnInitStage
{
public:
    // 建構函數
    InitLogger() : OnInitStage(ONINIT_STAGE_START) {}

    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化日誌記錄器階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            return INIT_FAILED;
        }

        if(!this.IsRegistered()) {
            error_handler.HandleError("初始化日誌記錄器階段註冊失敗");
            return INIT_FAILED;
        }

        // 創建日誌記錄器實例
        CFileLog* logger = new CFileLog(DEFAULT_LOG_FILE_NAME, DEFAULT_LOG_LEVEL, DEFAULT_PRINT_TO_CONSOLE, DEFAULT_APPEND_TO_EXISTING);
        if(logger == NULL) {
            error_handler.HandleError("無法創建日誌記錄器實例");
            return INIT_FAILED;
        }

        // 註冊日誌記錄器
        if(!Register("Logger", "日誌記錄器", logger, "Logger"))
        {
            error_handler.HandleError("日誌記錄器註冊失敗");
            delete logger; // 釋放資源
            return INIT_FAILED;
        }

        // 記錄初始化成功信息
        logger.Info("EA_Wizard 日誌記錄器初始化成功");

        Print("日誌記錄器初始化成功");
        return INIT_SUCCEEDED;
    }
}init_logger_stage;
