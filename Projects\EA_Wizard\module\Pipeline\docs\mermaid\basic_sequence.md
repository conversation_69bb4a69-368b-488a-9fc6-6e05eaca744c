```mermaid
sequenceDiagram
    participant Client
    participant Manager as PipelineManager
    participant Stage1 as 階段1
    participant Stage2 as 階段2
    participant Stage3 as 階段3
    participant Registry as Registry

    Client->>Manager: 創建管理器
    Manager->>Registry: 獲取註冊器
    Client->>Stage1: 創建階段1
    Client->>Stage2: 創建階段2
    Client->>Stage3: 創建階段3
    Client->>Manager: 添加階段1
    Client->>Manager: 添加階段2
    Client->>Manager: 添加階段3
    Client->>Manager: Execute()
    Manager->>Stage1: Execute(input)
    Stage1-->>Manager: 返回結果
    Manager->>Stage2: Execute(input)
    Stage2-->>Manager: 返回結果
    Manager->>Stage3: Execute(input)
    Stage3-->>Manager: 返回結果
    Manager-->>Client: 返回最終結果
    Client->>Manager: 釋放資源
```
