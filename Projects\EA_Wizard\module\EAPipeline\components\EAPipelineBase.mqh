#property strict

#include "../../PipelineAdvance/interface/IPipeline.mqh"
#include "../../PipelineAdvance/Pipeline.mqh"

//+------------------------------------------------------------------+
//| EAPipelineBase 類 - 裝飾者模式實現                               |
//+------------------------------------------------------------------+
class EAPipelineBase : public IPipeline
{
private:
    Pipeline* m_wrappee;  // 被裝飾的流水線
    string m_name;        // 流水線名稱
    string m_type;        // 流水線類型

public:
    // 構造函數
    EAPipelineBase(Pipeline* pipeline, string type = "EAPipelineBase")
        : m_wrappee(pipeline), 
          m_name(pipeline.GetName()), 
          m_type(type)
    {
    }
    
    EAPipelineBase(EAPipelineBase* pipeline, string type = "EAPipelineBase")
        : m_wrappee(NULL), 
          m_name(pipeline.GetName()), 
          m_type(type)
    {
        m_wrappee = pipeline.GetPipeline();
    }
    
    // 析構函數
    virtual ~EAPipelineBase()
    {
        // 注意：不刪除被裝飾的流水線，因為它可能被其他對象引用
    }

    // 執行流水線
    virtual void Execute() override
    {
        // 檢查是否已執行
        if(IsExecuted())
        {
            // 如果已執行，直接返回，不重複執行
            return;
        }

        // 檢查被裝飾的流水線是否為空
        if(m_wrappee == NULL)
        {
            return;
        }

        // 執行被裝飾的流水線
        m_wrappee.Execute();
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return m_type;
    }

    // 獲取流水線執行結果
    virtual PipelineResult* GetResult() override
    {
        // 直接返回被裝飾的流水線的結果
        return m_wrappee != NULL ? m_wrappee.GetResult() : NULL;
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        // 重置被裝飾的流水線
        if(m_wrappee != NULL)
        {
            m_wrappee.Restore();
        }
    }

    // 檢查流水線是否已執行
    virtual bool IsExecuted() override
    {
        // 直接返回被裝飾的流水線的執行狀態
        return m_wrappee != NULL ? m_wrappee.IsExecuted() : false;
    }

    // 獲取被裝飾的流水線
    virtual Pipeline* GetPipeline()
    {
        return m_wrappee;
    }

protected:
    // 設置被裝飾的流水線
    virtual void SetPipeline(Pipeline* pipeline)
    {
        m_wrappee = pipeline;
    }
};
