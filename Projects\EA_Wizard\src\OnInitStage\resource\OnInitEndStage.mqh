#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"

// OnInit 結束階段
class OnInitEndStage : public OnInitPipeline
{
public:
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 顯示初始化成功信息
        Print("EA初始化完成");
        
        // 可以在這裡添加其他初始化完成後的操作
        // 例如：顯示歡迎信息、設置圖表屬性等
        
        // 在圖表上顯示信息
        Comment("EA初始化成功，準備交易");
        
        // 設置圖表屬性
        ChartSetInteger(0, CHART_AUTOSCROLL, true);
        ChartSetInteger(0, CHART_SHIFT, true);
        ChartSetInteger(0, CHART_SHOW_GRID, false);
        ChartSetInteger(0, CHART_SHOW_ASK_LINE, true);
        ChartSetInteger(0, CHART_SHOW_BID_LINE, true);
        
        return INIT_SUCCEEDED;
    }
};
