#property strict

#include "../../module/mql4-lib-master/Trade/Order.mqh"
#include "../Enum/OrderEnum.mqh"
#include "../Util/EnumMap.mqh"

//+------------------------------------------------------------------+
//| 訂單屬性函數指針類型                                              |
//+------------------------------------------------------------------+
// 整數型
typedef int (*OrderIntFunc)();
// 浮點型
typedef double (*OrderDoubleFunc)();
// 字符串型
typedef string (*OrderStringFunc)();
// 日期時間型
typedef datetime (*OrderDateTimeFunc)();

//+------------------------------------------------------------------+
//| 訂單屬性函數                                                      |
//+------------------------------------------------------------------+
class OrderFunctions
{
public:
    // 訂單屬性函數
    static int Ticket() { return OrderTicket(); }
    static string Symbol() { return OrderSymbol(); }
    static int Type() { return OrderType(); }
    static double Lots() { return OrderLots(); }
    static double OpenPrice() { return OrderOpenPrice(); }
    static datetime OpenTime() { return OrderOpenTime(); }
    static double StopLoss() { return OrderStopLoss(); }
    static double TakeProfit() { return OrderTakeProfit(); }
    static datetime CloseTime() { return OrderCloseTime(); }
    static double ClosePrice() { return OrderClosePrice(); }
    static double Commission() { return OrderCommission(); }
    static double Swap() { return OrderSwap(); }
    static double Profit() { return OrderProfit(); }
    static string Comment() { return OrderComment(); }
    static int MagicNumber() { return OrderMagicNumber(); }
    static datetime Expiration() { return OrderExpiration(); }
};

class OrderIntFuncMap : public EnumMap<ENUM_ORDER_PROPERTY, OrderIntFunc>
{
public:
    OrderIntFuncMap() : EnumMap<ENUM_ORDER_PROPERTY, OrderIntFunc>(NULL, true)
    {
        // 初始化映射
        set(ORDER_PROPERTY_TICKET, OrderFunctions::Ticket);
        set(ORDER_PROPERTY_TYPE, OrderFunctions::Type);
        set(ORDER_PROPERTY_MAGIC_NUMBER, OrderFunctions::MagicNumber);
    }
};

class OrderDoubleFuncMap : public EnumMap<ENUM_ORDER_PROPERTY, OrderDoubleFunc>
{
public:
    OrderDoubleFuncMap() : EnumMap<ENUM_ORDER_PROPERTY, OrderDoubleFunc>(NULL, true)
    {
        // 初始化映射
        set(ORDER_PROPERTY_LOTS, OrderFunctions::Lots);
        set(ORDER_PROPERTY_OPEN_PRICE, OrderFunctions::OpenPrice);
        set(ORDER_PROPERTY_STOP_LOSS, OrderFunctions::StopLoss);
        set(ORDER_PROPERTY_TAKE_PROFIT, OrderFunctions::TakeProfit);
        set(ORDER_PROPERTY_CLOSE_PRICE, OrderFunctions::ClosePrice);
        set(ORDER_PROPERTY_COMMISSION, OrderFunctions::Commission);
        set(ORDER_PROPERTY_SWAP, OrderFunctions::Swap);
        set(ORDER_PROPERTY_PROFIT, OrderFunctions::Profit);
    }
};

class OrderStringFuncMap : public EnumMap<ENUM_ORDER_PROPERTY, OrderStringFunc>
{
public:
    OrderStringFuncMap() : EnumMap<ENUM_ORDER_PROPERTY, OrderStringFunc>(NULL, true)
    {
        // 初始化映射
        set(ORDER_PROPERTY_SYMBOL, OrderFunctions::Symbol);
        set(ORDER_PROPERTY_COMMENT, OrderFunctions::Comment);
    }
};

class OrderDateTimeFuncMap : public EnumMap<ENUM_ORDER_PROPERTY, OrderDateTimeFunc>
{
public:
    OrderDateTimeFuncMap() : EnumMap<ENUM_ORDER_PROPERTY, OrderDateTimeFunc>(NULL, true)
    {
        // 初始化映射
        set(ORDER_PROPERTY_OPEN_TIME, OrderFunctions::OpenTime);
        set(ORDER_PROPERTY_CLOSE_TIME, OrderFunctions::CloseTime);
        set(ORDER_PROPERTY_EXPIRATION, OrderFunctions::Expiration);
    }
};

class OrderFunctionMapper
{
private:
    OrderIntFuncMap* m_intMap;
    OrderDoubleFuncMap* m_doubleMap;
    OrderStringFuncMap* m_stringMap;
    OrderDateTimeFuncMap* m_dateTimeMap;

    static OrderFunctionMapper* g_instance;

    OrderFunctionMapper() {
        m_intMap = new OrderIntFuncMap();
        m_doubleMap = new OrderDoubleFuncMap();
        m_stringMap = new OrderStringFuncMap();
        m_dateTimeMap = new OrderDateTimeFuncMap();
    }
public:
    static OrderFunctionMapper* GetInstance()
    {
        if(g_instance == NULL)
        {
            g_instance = new OrderFunctionMapper();
        }
        return g_instance;
    }

    OrderIntFunc GetIntFunc(ENUM_ORDER_PROPERTY property)
    {
        return m_intMap.get(property, NULL);
    }

    OrderDoubleFunc GetDoubleFunc(ENUM_ORDER_PROPERTY property)
    {
        return m_doubleMap.get(property, NULL);
    }

    OrderStringFunc GetStringFunc(ENUM_ORDER_PROPERTY property)
    {
        return m_stringMap.get(property, NULL);
    }

    OrderDateTimeFunc GetDateTimeFunc(ENUM_ORDER_PROPERTY property)
    {
        return m_dateTimeMap.get(property, NULL);
    }
};

OrderFunctionMapper* OrderFunctionMapper::g_instance = NULL;
