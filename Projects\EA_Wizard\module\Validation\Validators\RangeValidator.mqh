#property strict

#include "../IValidator.mqh"
#include "../ValidationConfig.mqh"

//+------------------------------------------------------------------+
//| 範圍驗證器                                                         |
//| 用於驗證數值是否在指定範圍內                                         |
//+------------------------------------------------------------------+
template<typename T>
class CRangeValidator : public CBaseValidator
{
private:
    T m_value;        // 要驗證的值
    T m_min;          // 最小值
    T m_max;          // 最大值
    string m_field;   // 欄位名稱

public:
    // 建構函數
    CRangeValidator(T value, T min, T max, const string field)
        : CBaseValidator(VALIDATOR_NAME_RANGE, VALIDATOR_TYPE_RANGE)
        , m_value(value)
        , m_min(min)
        , m_max(max)
        , m_field(field)
    {}

    // 執行驗證
    virtual CValidationResult* Validate() override
    {
        CValidationResult* result = new CValidationResult();
        
        // 檢查值是否在範圍內
        if(m_value < m_min || m_value > m_max)
        {
            string minStr = (string)m_min;
            string maxStr = (string)m_max;
            
            result.SetInvalid(
                m_field, 
                StringFormat(VALIDATOR_MSG_RANGE_ERROR, minStr, maxStr),
                VALIDATOR_SOURCE_DEFAULT,
                VALIDATOR_ERROR_RANGE
            );
        }
        
        return result;
    }
    
    // 設置要驗證的值
    void SetValue(const T value)
    {
        m_value = value;
    }
    
    // 獲取要驗證的值
    T GetValue() const
    {
        return m_value;
    }
    
    // 設置最小值
    void SetMin(const T min)
    {
        m_min = min;
    }
    
    // 獲取最小值
    T GetMin() const
    {
        return m_min;
    }
    
    // 設置最大值
    void SetMax(const T max)
    {
        m_max = max;
    }
    
    // 獲取最大值
    T GetMax() const
    {
        return m_max;
    }
    
    // 設置欄位名稱
    void SetField(const string field)
    {
        m_field = field;
    }
    
    // 獲取欄位名稱
    string GetField() const
    {
        return m_field;
    }
};
