# PipelineAdvance 模組流程圖 (v1)

本文檔展示了 PipelineAdvance 模組的主要流程，包括流水線的執行、添加子流水線和移除子流水線等操作。

## 流水線執行流程

```mermaid
flowchart TD
    A[開始執行流水線] --> B{檢查是否已執行}
    B -->|已執行| C[直接返回]
    B -->|未執行| D{檢查是否有子流水線}
    D -->|沒有子流水線| E[設置成功結果並返回]
    D -->|有子流水線| F[檢查所有子流水線是否都未執行]
    F -->|有已執行的子流水線| G[設置失敗結果並返回]
    F -->|所有子流水線都未執行| H[執行第一個子流水線]
    H --> I{檢查子流水線結果}
    I -->|失敗| J[設置失敗結果並返回]
    I -->|成功| K{是否還有子流水線}
    K -->|是| L[執行下一個子流水線]
    L --> I
    K -->|否| M[設置成功結果並返回]
    C --> N[結束]
    E --> N
    G --> N
    J --> N
    M --> N
```

## 添加子流水線流程

```mermaid
flowchart TD
    A[開始添加子流水線] --> B{檢查子流水線是否為空}
    B -->|為空| C[返回失敗結果]
    B -->|不為空| D{檢查是否超過最大數量}
    D -->|超過| E[返回失敗結果]
    D -->|未超過| F[添加到子流水線向量]
    F -->|添加成功| G[返回成功結果]
    F -->|添加失敗| H[返回失敗結果]
    C --> I[結束]
    E --> I
    G --> I
    H --> I
```

## 移除子流水線流程

```mermaid
flowchart TD
    A[開始移除子流水線] --> B{檢查子流水線是否為空}
    B -->|為空| C[返回失敗結果]
    B -->|不為空| D[從子流水線向量中移除]
    D -->|移除成功| E[返回成功結果]
    D -->|移除失敗| F[返回失敗結果]
    C --> G[結束]
    E --> G
    F --> G
```

## 重置流水線流程

```mermaid
flowchart TD
    A[開始重置流水線] --> B[清理結果對象]
    B --> C[創建新的默認結果]
    C --> D[重置所有子流水線]
    D --> E[重置執行狀態]
    E --> F[結束]
```

## 設置結果流程

```mermaid
flowchart TD
    A[開始設置結果] --> B[清理現有結果]
    B --> C{檢查來源是否為空}
    C -->|為空| D[使用流水線名稱作為來源]
    C -->|不為空| E[使用指定來源]
    D --> F[創建新的結果對象]
    E --> F
    F --> G[設置執行狀態為已執行]
    G --> H[結束]
```
