```mermaid
sequenceDiagram
    participant Client
    participant Manager as PipelineManager
    participant Stage1 as 階段1
    participant Stage2 as 階段2(帶註冊器)
    participant Stage3 as 階段3(帶註冊器)
    participant Registry as Registry

    Client->>Manager: 創建管理器
    Manager->>Registry: 創建註冊器
    Client->>Stage1: 創建階段1
    Client->>Stage2: 創建階段2(傳入管理器)
    Client->>Stage3: 創建階段3(傳入管理器)
    Client->>Manager: 添加階段1
    Client->>Manager: 添加階段2
    Client->>Manager: 添加階段3
    Client->>Manager: Execute()
    Manager->>Stage1: Execute(input)
    Stage1-->>Manager: 返回結果
    Manager->>Stage2: Execute(input)
    Stage2->>Registry: 註冊數據
    Registry-->>Stage2: 返回註冊結果
    Stage2-->>Manager: 返回結果
    Manager->>Stage3: Execute(input)
    Stage3->>Registry: 獲取數據
    Registry-->>Stage3: 返回數據
    Stage3-->>Manager: 返回結果
    Manager-->>Client: 返回最終結果
    Client->>Manager: 釋放資源
```
