#property strict

#include "OrderFunc.mqh"
#include "../Enum/CompareEnum.mqh"
#include "../../module/mql4-lib-master/Trade/Order.mqh"
#include "../../module/mql4-lib-master/Collection/Vector.mqh"

class OrderNullMatcher : public OrderMatcher
{
public:
    bool matches() const { return true; }
};

//+------------------------------------------------------------------+
//| 訂單匹配器鏈 - 組合多個匹配器                                     |
//+------------------------------------------------------------------+
class OrderMatcherChain : public OrderMatcher
{
private:
    List<OrderMatcher*>* m_matchers; // 匹配器列表

public:
    // 建構函式
    OrderMatcherChain()
    {
        m_matchers = new Vector<OrderMatcher*>();
    }

    // 建構函式 - 使用現有列表
    OrderMatcherChain(List<OrderMatcher*>* matchers)
    {
        m_matchers = matchers;
    }

    // 新增匹配器
    void add(OrderMatcher* matcher)
    {
        if(matcher != NULL)
        {
            m_matchers.push(matcher);
        }
    }

    // 實現 matches 方法 - 所有匹配器都必須匹配才返回 true
    bool matches() const override
    {
        for(int i = 0; i < m_matchers.size(); i++)
        {
            if(!m_matchers.get(i).matches())
            {
                return false;
            }
        }
        return true;
    }

    // 析構函式
    ~OrderMatcherChain()
    {
        m_matchers.clear();
        delete m_matchers;
        m_matchers = NULL;
    }
};

template <typename Pointer, typename T>
class OrderGeneralMatcher : public OrderMatcher
{
protected:
    Pointer m_pointer;  // 訂單屬性指針
    T m_value;      // 匹配值

public:
    // 建構函式
    OrderGeneralMatcher(Pointer pointer, T value)
        : m_pointer(pointer), m_value(value) {}

    // 實現 matches 方法
    virtual bool matches() const override
    {
        return m_pointer() == m_value;
    }

};

//+------------------------------------------------------------------+
//| 整數型訂單屬性匹配器                                              |
//+------------------------------------------------------------------+
class OrderIntPropertyMatcher : public OrderGeneralMatcher<OrderIntFunc, int>
{
private:
    ENUM_COMPARER m_compareMode;

public:
    OrderIntPropertyMatcher(OrderIntFunc pointer, int value, ENUM_COMPARER compareMode = COMPARER_EQUAL)
        : OrderGeneralMatcher(pointer, value), m_compareMode(compareMode) {}

    bool matches() const override
    {
        int currentValue = m_pointer();

        switch(m_compareMode)
        {
            case COMPARER_EQUAL: return currentValue == m_value;
            case COMPARER_GREATER: return currentValue > m_value;
            case COMPARER_LESS: return currentValue < m_value;
            case COMPARER_GREATER_EQUAL: return currentValue >= m_value;
            case COMPARER_LESS_EQUAL: return currentValue <= m_value;
            case COMPARER_NOT_EQUAL: return currentValue != m_value;
            default: return false;
        }
    }
};

//+------------------------------------------------------------------+
//| 浮點型訂單屬性匹配器                                              |
//+------------------------------------------------------------------+
class OrderDoublePropertyMatcher : public OrderGeneralMatcher<OrderDoubleFunc, double>
{
private:
    ENUM_COMPARER m_compareMode;
    double m_tolerance;

public:
    OrderDoublePropertyMatcher(OrderDoubleFunc pointer, double value, ENUM_COMPARER compareMode = COMPARER_EQUAL, double tolerance = 0.00001)
        : OrderGeneralMatcher(pointer, value), m_compareMode(compareMode), m_tolerance(tolerance) {}

    bool matches() const override
    {
        double currentValue = m_pointer();

        switch(m_compareMode)
        {
            case COMPARER_EQUAL: return MathAbs(currentValue - m_value) <= m_tolerance;
            case COMPARER_GREATER: return currentValue > m_value;
            case COMPARER_LESS: return currentValue < m_value;
            case COMPARER_GREATER_EQUAL: return currentValue >= m_value;
            case COMPARER_LESS_EQUAL: return currentValue <= m_value;
            case COMPARER_NOT_EQUAL: return MathAbs(currentValue - m_value) > m_tolerance;
            default: return false;
        }
    }
};

//+------------------------------------------------------------------+
//| 字符串型訂單屬性匹配器                                            |
//+------------------------------------------------------------------+
class OrderStringPropertyMatcher : public OrderGeneralMatcher<OrderStringFunc, string>
{
private:
    bool m_caseSensitive;
    bool m_exactMatch;

public:
    OrderStringPropertyMatcher(OrderStringFunc pointer, string value, bool caseSensitive = true, bool exactMatch = true)
        : OrderGeneralMatcher(pointer, value), m_caseSensitive(caseSensitive), m_exactMatch(exactMatch) {}

    bool matches() const override
    {
        string currentValue = m_pointer();

        if(!m_caseSensitive)
        {
            string lowerCurrentValue = currentValue;
            StringToLower(lowerCurrentValue);

            string compareValue = m_value;
            StringToLower(compareValue);

            if(m_exactMatch)
                return lowerCurrentValue == compareValue;
            else
                return StringFind(lowerCurrentValue, compareValue) >= 0;
        }
        else
        {
            if(m_exactMatch)
                return currentValue == m_value;
            else
                return StringFind(currentValue, m_value) >= 0;
        }
    }
};

//+------------------------------------------------------------------+
//| 日期時間型訂單屬性匹配器                                          |
//+------------------------------------------------------------------+
class OrderDateTimePropertyMatcher : public OrderGeneralMatcher<OrderDateTimeFunc, datetime>
{
private:
    ENUM_COMPARER m_compareMode;

public:
    OrderDateTimePropertyMatcher(OrderDateTimeFunc pointer, datetime value, ENUM_COMPARER compareMode = COMPARER_EQUAL)
        : OrderGeneralMatcher(pointer, value), m_compareMode(compareMode) {}

    bool matches() const override
    {
        datetime currentValue = m_pointer();

        switch(m_compareMode)
        {
            case COMPARER_EQUAL: return currentValue == m_value;
            case COMPARER_GREATER: return currentValue > m_value;
            case COMPARER_LESS: return currentValue < m_value;
            case COMPARER_GREATER_EQUAL: return currentValue >= m_value;
            case COMPARER_LESS_EQUAL: return currentValue <= m_value;
            case COMPARER_NOT_EQUAL: return currentValue != m_value;
            default: return false;
        }
    }
};

//+------------------------------------------------------------------+
//| 訂單匹配器建構器 - 用於逐步建構複雜匹配器                          |
//+------------------------------------------------------------------+
class OrderMatcherBuilder
{
private:
    OrderMatcher* m_matcher;         // 基本匹配器
    OrderMatcherChain* m_matcherChain; // 匹配器鏈

public:
    // 建構函式
    OrderMatcherBuilder()
    {
        m_matcher = new OrderNullMatcher();
        m_matcherChain = new OrderMatcherChain();
    }

    // 重置建構器
    void Reset()
    {
        delete m_matcher;
        delete m_matcherChain;

        m_matcher = new OrderNullMatcher();
        m_matcherChain = new OrderMatcherChain();
    }

    // 添加匹配器
    void AddMatcher(OrderMatcher* matcher)
    {
        if(matcher != NULL)
        {
            m_matcherChain.add(matcher);
        }
    }

    // 建構最終的匹配器
    OrderMatcher* Build()
    {
        // 如果沒有添加任何匹配器，返回空匹配器
        if(m_matcherChain == NULL)
        {
            return m_matcher;
        }

        // 返回匹配器鏈
        return m_matcherChain;
    }

    // 析構函式
    ~OrderMatcherBuilder()
    {
        delete m_matcher;
        delete m_matcherChain;

        m_matcher = NULL;
        m_matcherChain = NULL;
    }
};

//+------------------------------------------------------------------+
//| 訂單匹配器工廠 - 創建各種類型的匹配器                             |
//+------------------------------------------------------------------+
class OrderMatcherFactory
{
public:
    // 創建票號匹配器
    static OrderMatcher* CreateTicketMatcher(int ticket, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderIntPropertyMatcher(OrderFunctions::Ticket, ticket, compareMode);
    }

    // 創建交易品種匹配器
    static OrderMatcher* CreateSymbolMatcher(string symbol, bool caseSensitive = true, bool exactMatch = true)
    {
        return new OrderStringPropertyMatcher(OrderFunctions::Symbol, symbol, caseSensitive, exactMatch);
    }

    // 創建訂單類型匹配器
    static OrderMatcher* CreateTypeMatcher(int type, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderIntPropertyMatcher(OrderFunctions::Type, type, compareMode);
    }

    // 創建手數匹配器
    static OrderMatcher* CreateLotsMatcher(double lots, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::Lots, lots, COMPARER_EQUAL, tolerance);
    }

    // 創建開倉價格匹配器
    static OrderMatcher* CreateOpenPriceMatcher(double price, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::OpenPrice, price, COMPARER_EQUAL, tolerance);
    }

    // 創建開倉時間匹配器
    static OrderMatcher* CreateOpenTimeMatcher(datetime time, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderDateTimePropertyMatcher(OrderFunctions::OpenTime, time, compareMode);
    }

    // 創建止損匹配器
    static OrderMatcher* CreateStopLossMatcher(double stopLoss, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::StopLoss, stopLoss, COMPARER_EQUAL, tolerance);
    }

    // 創建止盈匹配器
    static OrderMatcher* CreateTakeProfitMatcher(double takeProfit, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::TakeProfit, takeProfit, COMPARER_EQUAL, tolerance);
    }

    // 創建平倉價格匹配器
    static OrderMatcher* CreateClosePriceMatcher(double price, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::ClosePrice, price, COMPARER_EQUAL, tolerance);
    }

    // 創建平倉時間匹配器
    static OrderMatcher* CreateCloseTimeMatcher(datetime time, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderDateTimePropertyMatcher(OrderFunctions::CloseTime, time, compareMode);
    }

    // 創建佣金匹配器
    static OrderMatcher* CreateCommissionMatcher(double commission, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::Commission, commission, COMPARER_EQUAL, tolerance);
    }

    // 創建掉期匹配器
    static OrderMatcher* CreateSwapMatcher(double swap, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::Swap, swap, COMPARER_EQUAL, tolerance);
    }

    // 創建利潤匹配器
    static OrderMatcher* CreateProfitMatcher(double profit, double tolerance = 0.00001)
    {
        return new OrderDoublePropertyMatcher(OrderFunctions::Profit, profit, COMPARER_EQUAL, tolerance);
    }

    // 創建註釋匹配器
    static OrderMatcher* CreateCommentMatcher(string comment, bool caseSensitive = true, bool exactMatch = true)
    {
        return new OrderStringPropertyMatcher(OrderFunctions::Comment, comment, caseSensitive, exactMatch);
    }

    // 創建魔術數字匹配器
    static OrderMatcher* CreateMagicNumberMatcher(int magic, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderIntPropertyMatcher(OrderFunctions::MagicNumber, magic, compareMode);
    }

    // 創建到期時間匹配器
    static OrderMatcher* CreateExpirationMatcher(datetime expiration, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        return new OrderDateTimePropertyMatcher(OrderFunctions::Expiration, expiration, compareMode);
    }

    static OrderMatcher* CreateMatcher(ENUM_ORDER_PROPERTY property, int value, int tolerance = 0)
    {
        switch(property)
        {
            case ORDER_PROPERTY_TICKET: return CreateTicketMatcher(value);
            case ORDER_PROPERTY_TYPE: return CreateTypeMatcher(value);
            case ORDER_PROPERTY_MAGIC_NUMBER: return CreateMagicNumberMatcher(value);
            default: return new OrderNullMatcher();
        }
    }

    static OrderMatcher* CreateMatcher(ENUM_ORDER_PROPERTY property, double value, double tolerance = 0.00001)
    {
        switch(property)
        {
            case ORDER_PROPERTY_LOTS: return CreateLotsMatcher(value, tolerance);
            case ORDER_PROPERTY_OPEN_PRICE: return CreateOpenPriceMatcher(value, tolerance);
            case ORDER_PROPERTY_STOP_LOSS: return CreateStopLossMatcher(value, tolerance);
            case ORDER_PROPERTY_TAKE_PROFIT: return CreateTakeProfitMatcher(value, tolerance);
            case ORDER_PROPERTY_CLOSE_PRICE: return CreateClosePriceMatcher(value, tolerance);
            case ORDER_PROPERTY_COMMISSION: return CreateCommissionMatcher(value, tolerance);
            case ORDER_PROPERTY_SWAP: return CreateSwapMatcher(value, tolerance);
            case ORDER_PROPERTY_PROFIT: return CreateProfitMatcher(value, tolerance);
            default: return new OrderNullMatcher();
        }
    }

    static OrderMatcher* CreateMatcher(ENUM_ORDER_PROPERTY property, string value, bool caseSensitive = true, bool exactMatch = true)
    {
        switch(property)
        {
            case ORDER_PROPERTY_SYMBOL: return CreateSymbolMatcher(value, caseSensitive, exactMatch);
            case ORDER_PROPERTY_COMMENT: return CreateCommentMatcher(value, caseSensitive, exactMatch);
            default: return new OrderNullMatcher();
        }
    }

    static OrderMatcher* CreateMatcher(ENUM_ORDER_PROPERTY property, datetime value, ENUM_COMPARER compareMode = COMPARER_EQUAL)
    {
        switch(property)
        {
            case ORDER_PROPERTY_OPEN_TIME: return CreateOpenTimeMatcher(value, compareMode);
            case ORDER_PROPERTY_CLOSE_TIME: return CreateCloseTimeMatcher(value, compareMode);
            case ORDER_PROPERTY_EXPIRATION: return CreateExpirationMatcher(value, compareMode);
            default: return new OrderNullMatcher();
        }
    }
};