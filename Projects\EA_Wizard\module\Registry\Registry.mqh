//+------------------------------------------------------------------+
//|                                                    Registry.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib-master/Collection/HashMap.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"
#include "RegistryItem.mqh"

//+------------------------------------------------------------------+
//| 註冊器基類 - 用於註冊和管理各種類型的對象                       |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
class Registry
{
protected:
    HashMap<Key, IRegistryItem<Val>*>* m_items; // 存儲註冊項目的哈希表
    string m_name;                   // 註冊器名稱
    int m_maxItems;                  // 最大項目數量
    int m_itemCounter;               // 項目計數器（用於生成唯一ID）
    Key m_lastRegisteredKey;         // 最後註冊的鍵

    public:
    // 構造函數
    Registry(const string name = "DefaultRegistry", const int maxItems = 100)
        : m_name(name), m_maxItems(maxItems), m_itemCounter(0)
    {
        m_items = new HashMap<Key, IRegistryItem<Val>*>(NULL, true); // 創建哈希表，自動刪除值
    }

    // 析構函數
    ~Registry()
    {
        if(m_items != NULL)
        {
            delete m_items;
            m_items = NULL;
        }
    }

    // 註冊新項目（抽象方法，由子類實現）
    virtual bool Register(const string name, const string description, Val value, Key key = NULL) = 0;

    // 生成唯一ID（由子類實現）
    virtual Key GenerateKey() = 0;

    // 將鍵轉換為字符串（由子類實現）
    virtual string KeyToString(const Key id) = 0;

    // 根據ID獲取項目
    IRegistryItem<Val>* GetItem(const Key id)
    {
        IRegistryItem<Val>* item = NULL;
        if(!m_items.contains((Key)id))
        {
            Print("項目不存在：", KeyToString(id));
            return NULL;
        }

        return m_items.get((Key)id, NULL);
    }

    // 根據ID獲取項目值
    Val GetValue(const Key id, Val defaultValue = NULL)
    {
        IRegistryItem<Val>* item = GetItem(id);
        if(item == NULL)
        {
            return defaultValue;
        }

        return item.GetValue();
    }

    // 更新項目值
    bool UpdateValue(const Key id, Val newValue)
    {
        IRegistryItem<Val>* item = GetItem(id);
        if(item == NULL)
        {
            return false;
        }

        // 由於 IRegistryItem 不包含 Setter，我們需要創建一個新的項目
        string idStr = item.GetId();
        string name = item.GetName();
        string description = item.GetDescription();

        // 創建新項目
        IRegistryItem<Val>* newItem = new RegistryItem<Val>(idStr, name, description, newValue);

        // 更新哈希表
        m_items.set((Key)id, newItem);

        Print("項目值已更新：", KeyToString(id));
        return true;
    }

    // 更新項目名稱
    bool UpdateName(const Key id, const string newName)
    {
        IRegistryItem<Val>* item = GetItem(id);
        if(item == NULL)
        {
            return false;
        }

        // 由於 IRegistryItem 不包含 Setter，我們需要創建一個新的項目
        string idStr = item.GetId();
        string description = item.GetDescription();
        Val value = item.GetValue();

        // 創建新項目
        IRegistryItem<Val>* newItem = new RegistryItem<Val>(idStr, newName, description, value);

        // 更新哈希表
        m_items.set((Key)id, newItem);

        Print("項目名稱已更新：", KeyToString(id));
        return true;
    }

    // 更新項目描述
    bool UpdateDescription(const Key id, const string newDescription)
    {
        IRegistryItem<Val>* item = GetItem(id);
        if(item == NULL)
        {
            return false;
        }

        // 由於 IRegistryItem 不包含 Setter，我們需要創建一個新的項目
        string idStr = item.GetId();
        string name = item.GetName();
        Val value = item.GetValue();

        // 創建新項目
        IRegistryItem<Val>* newItem = new RegistryItem<Val>(idStr, name, newDescription, value);

        // 更新哈希表
        m_items.set((Key)id, newItem);

        Print("項目描述已更新：", KeyToString(id));
        return true;
    }

    // 移除項目
    bool Unregister(const Key id)
    {
        if(!m_items.contains((Key)id))
        {
            Print("項目不存在，無法移除：", KeyToString(id));
            return false;
        }

        m_items.remove((Key)id);
        Print("項目已移除：", KeyToString(id));
        return true;
    }

    // 獲取所有項目ID
    void GetAllIds(Key &ids[])
    {
        Collection<Key>* keys = new Vector<Key>();
        m_items.keys(keys);

        int size = keys.size();
        ArrayResize(ids, size);

        int i = 0;
        foreachv(Key, key, keys)
        {
            ids[i++] = key;
        }

        delete keys;
    }

    // 獲取註冊項目數量
    int GetCount()
    {
        return m_items.size();
    }

    // 檢查是否包含指定ID的項目
    bool Contains(const Key id)
    {
        return m_items.contains((Key)id);
    }

    // 清空註冊器
    void Clear()
    {
        m_items.clear();
        m_itemCounter = 0;
        Print("註冊器已清空：", m_name);
    }

    // 獲取註冊器名稱
    string GetName() const
    {
        return m_name;
    }

    // 設置註冊器名稱
    void SetName(const string name)
    {
        m_name = name;
    }

    // 獲取最大項目數量
    int GetMaxItems() const
    {
        return m_maxItems;
    }

    // 設置最大項目數量
    void SetMaxItems(const int maxItems)
    {
        if(maxItems > 0)
        {
            m_maxItems = maxItems;
        }
    }

    // 獲取最後註冊的鍵
    Key GetLastRegisteredKey() const
    {
        return m_lastRegisteredKey;
    }

    // 遍歷所有項目（使用回調函數）
    template<typename Callback>
    void ForEach(Callback callback)
    {
        foreachm(Key, id, IRegistryItem<Val>*, item, m_items)
        {
            callback(id, item);
        }
    }

    // 靜態註冊方法
    static bool RegisterToRegistry(
        Registry<Key,Val>* registry, 
        const string name, 
        const string description, 
        Val value, 
        Key key = NULL
    ) {
        if(registry == NULL) {
            Print("錯誤: 註冊表指針為空");
            return false;
        }
        
        return registry.Register(name, description, value, key);
    }

    // 批量註冊方法
    static bool RegisterMultiple(
        Registry<Key,Val>* registry,
        const IRegistryItem<Val>* &items[]
    ) {
        if(registry == NULL) return false;
        
        int size = ArraySize(items);
        if(size == 0) {
            Print("錯誤: 項目陣列為空");
            return false;
        }
        
        bool success = true;
        for(int i = 0; i < size; i++) {
            if(items[i] == NULL) {
                success = false;
                Print("警告: 項目為空 - 索引 ", i);
                continue;
            }
            
            if(!registry.Register(
                items[i].GetName(),
                items[i].GetDescription(),
                items[i].GetValue(),
                NULL  // 使用自動生成的鍵
            )) {
                success = false;
                Print("警告: 註冊失敗 - ", items[i].GetName());
            }
        }
        
        return success;
    }
};

