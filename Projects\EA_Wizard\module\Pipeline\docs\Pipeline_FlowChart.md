# Pipeline 模組流程圖

## 流水線執行流程

```mermaid
flowchart TD
    A[開始執行流水線] --> B{檢查是否有階段}
    B -->|沒有階段| C[返回預設值]
    B -->|有階段| D[獲取所有階段]
    D --> E[執行第一個階段]
    E --> F{檢查結果}
    F -->|失敗且停止於錯誤| G[返回錯誤結果]
    F -->|成功或不停止於錯誤| H{是否還有階段}
    H -->|是| I[執行下一個階段]
    I --> F
    H -->|否| J[返回最後階段結果]
    C --> K[結束]
    G --> K
    J --> K
```

## OnInit 流水線執行流程

```mermaid
flowchart TD
    A[開始執行 OnInit 流水線] --> B{檢查是否有階段}
    B -->|沒有階段| C[返回 INIT_SUCCEEDED]
    B -->|有階段| D[獲取所有階段]
    D --> E[執行第一個階段]
    E --> F{檢查結果}
    F -->|INIT_FAILED 或 INIT_PARAMETERS_INCORRECT| G[返回錯誤結果]
    F -->|INIT_SUCCEEDED| H{是否還有階段}
    H -->|是| I[執行下一個階段]
    I --> F
    H -->|否| J[返回 INIT_SUCCEEDED]
    C --> K[結束]
    G --> K
    J --> K
```

## OnTick 流水線執行流程

```mermaid
flowchart TD
    A[開始執行 OnTick 流水線] --> B{檢查是否有階段}
    B -->|沒有階段| C[返回 true]
    B -->|有階段| D[獲取所有階段]
    D --> E[執行第一個階段]
    E --> F{檢查結果}
    F -->|false 且停止於錯誤| G[返回 false]
    F -->|true 或不停止於錯誤| H{是否還有階段}
    H -->|是| I[執行下一個階段]
    I --> F
    H -->|否| J[返回 true]
    C --> K[結束]
    G --> K
    J --> K
```

## OnDeinit 流水線執行流程

```mermaid
flowchart TD
    A[開始執行 OnDeinit 流水線] --> B{檢查是否有階段}
    B -->|沒有階段| C[返回 true]
    B -->|有階段| D[獲取所有階段]
    D --> E[執行第一個階段]
    E --> F{檢查結果}
    F -->|false 且停止於錯誤| G[返回 false]
    F -->|true 或不停止於錯誤| H{是否還有階段}
    H -->|是| I[執行下一個階段]
    I --> F
    H -->|否| J[返回 true]
    C --> K[結束]
    G --> K
    J --> K
```
