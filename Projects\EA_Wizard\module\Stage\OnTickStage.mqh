#property strict

#include "Enum.mqh"
#include "StageGroup.mqh"
#include "StageManager.mqh"
#include "../Util/Util.mqh"

// OnTick 階段群組已移至 StageGroup/OnTickStageGroup.mqh

// OnTick 開始階段
class OnTickStartStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static OnTickStartStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnTickStartStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    OnTickStartStage() : OnTickStageGroup("OnTickStartStage") {}

public:
    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("OnTick開始階段註冊成功");
            } else {
                Print("警告: OnTick開始階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("OnTick 開始執行");
        return true;
    }
};

// 市場數據更新階段
class MarketDataStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static MarketDataStage* instance = NULL;
        if(instance == NULL) {
            instance = new MarketDataStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;

    MarketDataStage() : OnTickStageGroup("MarketDataStage"),
        m_symbol(Symbol()),
        m_timeframe(Period()) {}

public:
    // 设置交易品种和时间周期
    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    void SetTimeframe(ENUM_TIMEFRAMES timeframe) {
        m_timeframe = timeframe;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("市場數據更新階段註冊成功");
            } else {
                Print("警告: 市場數據更新階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("市場數據更新成功: ", m_symbol, ", 時間週期: ", EnumToString(m_timeframe));
        return true;
    }
};

// 持倉狀態檢查階段
class PositionCheckStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static PositionCheckStage* instance = NULL;
        if(instance == NULL) {
            instance = new PositionCheckStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    int m_magic_number;
    string m_symbol;
    bool m_has_positions;

    PositionCheckStage() : OnTickStageGroup("PositionCheckStage"),
        m_magic_number(0),
        m_symbol(Symbol()),
        m_has_positions(false) {}

public:
    // 设置魔术数字和交易品种
    void SetMagicNumber(int magic_number) {
        m_magic_number = magic_number;
    }

    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    // 獲取是否有持倉
    bool HasPositions() const {
        return m_has_positions;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("持倉狀態檢查階段註冊成功");
            } else {
                Print("警告: 持倉狀態檢查階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("持倉狀態檢查階段執行成功");
        return true;
    }
};

// 使用 Enum.mqh 中定義的信號類型枚舉

// 交易信號檢查階段
class SignalCheckStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static SignalCheckStage* instance = NULL;
        if(instance == NULL) {
            instance = new SignalCheckStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;
    int m_signal_type;
    double m_signal_strength;
    bool m_has_signal;

    SignalCheckStage() : OnTickStageGroup("SignalCheckStage"),
        m_symbol(Symbol()),
        m_timeframe(Period()),
        m_signal_type(SIGNAL_NONE),
        m_signal_strength(0.0),
        m_has_signal(false) {}

public:
    // 设置交易品种和时间周期
    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    void SetTimeframe(ENUM_TIMEFRAMES timeframe) {
        m_timeframe = timeframe;
    }

    // 獲取信號類型
    int GetSignalType() const {
        return m_signal_type;
    }

    // 獲取信號強度
    double GetSignalStrength() const {
        return m_signal_strength;
    }

    // 獲取是否有信號
    bool HasSignal() const {
        return m_has_signal;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("信號檢查階段註冊成功");
            } else {
                Print("警告: 信號檢查階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("信號檢查階段執行成功");
        return true;
    }
};

// 風險控制檢查階段
class RiskCheckStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static RiskCheckStage* instance = NULL;
        if(instance == NULL) {
            instance = new RiskCheckStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    string m_symbol;
    int m_magic_number;
    double m_max_risk_percent;
    double m_max_positions;
    SignalCheckStage* m_signal_stage;
    bool m_risk_allowed;

    RiskCheckStage() : OnTickStageGroup("RiskCheckStage"),
        m_symbol(Symbol()),
        m_magic_number(0),
        m_max_risk_percent(2.0),
        m_max_positions(5),
        m_signal_stage(NULL),
        m_risk_allowed(false) {}

public:
    // 设置交易品种和魔术数字
    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    void SetMagicNumber(int magic_number) {
        m_magic_number = magic_number;
    }

    void SetMaxRiskPercent(double max_risk_percent) {
        m_max_risk_percent = max_risk_percent;
    }

    void SetMaxPositions(double max_positions) {
        m_max_positions = max_positions;
    }

    void SetSignalStage(SignalCheckStage* signal_stage) {
        m_signal_stage = signal_stage;
    }

    // 獲取風險是否允許
    bool IsRiskAllowed() const {
        return m_risk_allowed;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("風險控制檢查階段註冊成功");
            } else {
                Print("警告: 風險控制檢查階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("風險控制檢查階段執行成功");
        return true;
    }
};

// 交易執行階段
class TradeExecutionStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static TradeExecutionStage* instance = NULL;
        if(instance == NULL) {
            instance = new TradeExecutionStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    string m_symbol;
    int m_magic_number;
    double m_lot_size;
    int m_slippage;
    double m_stop_loss_pips;
    double m_take_profit_pips;
    SignalCheckStage* m_signal_stage;
    RiskCheckStage* m_risk_stage;
    int m_ticket;

    TradeExecutionStage() : OnTickStageGroup("TradeExecutionStage"),
        m_symbol(Symbol()),
        m_magic_number(0),
        m_lot_size(0.01),
        m_slippage(3),
        m_stop_loss_pips(20.0),
        m_take_profit_pips(40.0),
        m_signal_stage(NULL),
        m_risk_stage(NULL),
        m_ticket(-1) {}

public:
    // 设置交易参数
    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    void SetMagicNumber(int magic_number) {
        m_magic_number = magic_number;
    }

    void SetLotSize(double lot_size) {
        m_lot_size = lot_size;
    }

    void SetSlippage(int slippage) {
        m_slippage = slippage;
    }

    void SetStopLossPips(double stop_loss_pips) {
        m_stop_loss_pips = stop_loss_pips;
    }

    void SetTakeProfitPips(double take_profit_pips) {
        m_take_profit_pips = take_profit_pips;
    }

    void SetSignalStage(SignalCheckStage* signal_stage) {
        m_signal_stage = signal_stage;
    }

    void SetRiskStage(RiskCheckStage* risk_stage) {
        m_risk_stage = risk_stage;
    }

    // 獲取訂單號
    int GetTicket() const {
        return m_ticket;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("交易執行階段註冊成功");
            } else {
                Print("警告: 交易執行階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("交易執行階段執行成功");
        return true;
    }
};

// 日誌記錄和通知階段
class OnTickLoggingStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static OnTickLoggingStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnTickLoggingStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    string m_symbol;
    SignalCheckStage* m_signal_stage;
    TradeExecutionStage* m_trade_stage;
    bool m_use_alerts;
    bool m_use_push_notifications;
    bool m_use_email_notifications;

    OnTickLoggingStage() : OnTickStageGroup("OnTickLoggingStage"),
        m_symbol(Symbol()),
        m_signal_stage(NULL),
        m_trade_stage(NULL),
        m_use_alerts(true),
        m_use_push_notifications(false),
        m_use_email_notifications(false) {}

public:
    // 设置交易品种和通知选项
    void SetSymbol(string symbol) {
        m_symbol = symbol;
    }

    void SetSignalStage(SignalCheckStage* signal_stage) {
        m_signal_stage = signal_stage;
    }

    void SetTradeStage(TradeExecutionStage* trade_stage) {
        m_trade_stage = trade_stage;
    }

    void SetUseAlerts(bool use_alerts) {
        m_use_alerts = use_alerts;
    }

    void SetUsePushNotifications(bool use_push_notifications) {
        m_use_push_notifications = use_push_notifications;
    }

    void SetUseEmailNotifications(bool use_email_notifications) {
        m_use_email_notifications = use_email_notifications;
    }

    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("日誌記錄階段註冊成功");
            } else {
                Print("警告: 日誌記錄階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("日誌記錄階段執行成功");
        return true;
    }
};

// OnTick 結束階段
class OnTickEndStage : public OnTickStageGroup {
private:
    static OnTickStageGroup* s_instance;  // 单例实例
    static OnTickStageGroup* GetInstanceInternal() {
        static OnTickEndStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnTickEndStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    datetime m_last_tick_time;

    OnTickEndStage() : OnTickStageGroup("OnTickEndStage"),
        m_last_tick_time(0) {}

public:
    // 获取单例实例
    static OnTickStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<bool, void*>* manager = OnTickStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("OnTick結束階段註冊成功");
            } else {
                Print("警告: OnTick結束階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        bool result = OnTickStageGroup::Execute(in);
        if(!result) return false;

        Print("OnTick 結束階段執行成功");
        return true;
    }
};

// 靜態變數定義
OnTickStageGroup* OnTickStartStage::s_instance = NULL;
OnTickStageGroup* MarketDataStage::s_instance = NULL;
OnTickStageGroup* PositionCheckStage::s_instance = NULL;
OnTickStageGroup* SignalCheckStage::s_instance = NULL;
OnTickStageGroup* RiskCheckStage::s_instance = NULL;
OnTickStageGroup* TradeExecutionStage::s_instance = NULL;
OnTickStageGroup* OnTickLoggingStage::s_instance = NULL;
OnTickStageGroup* OnTickEndStage::s_instance = NULL;


class OnTickStageMapper
{
private:
    static OnTickStageMapper* s_instance;
    Map<int, OnTickStageGroupFunc>* m_map;

    OnTickStageMapper()
    {
        m_map = new HashMap<int, OnTickStageGroupFunc>();
        InitializeMap();
    }

    ~OnTickStageMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)ONTICK_STAGE_START, OnTickStartStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_MARKET_DATA, MarketDataStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_POSITION_CHECK, PositionCheckStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_SIGNAL_CHECK, SignalCheckStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_RISK_CHECK, RiskCheckStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_TRADE_EXECUTION, TradeExecutionStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_LOGGING, OnTickLoggingStage::GetInstance);
        m_map.set((int)ONTICK_STAGE_END, OnTickEndStage::GetInstance);
    }

public:
    static OnTickStageMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new OnTickStageMapper();
        }
        return s_instance;
    }

    OnTickStageGroupFunc GetStageFunction(ENUM_ONTICK_STAGE stage)
    {
        return m_map.get((int)stage,NULL);
    }
};

OnTickStageMapper* OnTickStageMapper::s_instance = NULL;
