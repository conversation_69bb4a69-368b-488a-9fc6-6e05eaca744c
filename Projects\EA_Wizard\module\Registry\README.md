# Registry 模組

這是一個簡單的註冊器模組，用於在 EA_Wizard 項目中註冊和管理各種類型的對象。

## 功能特點

- 支持註冊、查詢、更新和移除項目
- 使用泛型設計，可以註冊不同類型的對象
- 支持使用不同類型的鍵（string、int、enum 等）
- 實現單例模式，方便全局訪問
- 基於 mql4-lib-master 的 HashMap 實現高效存儲
- 支持項目元數據（ID、名稱、描述、創建時間、更新時間）
- 提供遍歷和回調機制

## 文件結構

- `Registry.mqh` - 主要的註冊器類實現
- `RegistryItem.mqh` - 註冊項目的基本結構
- `RegistryExample.mqh` - 基本使用示例
- `RegistryAdvancedExample.mqh` - 進階使用示例
- `RegistryTest.mq4` - 測試腳本

## 使用方法

### 基本用法（字符串鍵）

```cpp
// 獲取字符串註冊器實例
Registry<string, string>* stringRegistry = Registry<string, string>::GetInstance("StringRegistry");

// 註冊項目
string id = stringRegistry.Register("Symbol", "交易品種", "EURUSD");

// 獲取項目值
string symbol = stringRegistry.GetValue(id);

// 更新項目值
stringRegistry.UpdateValue(id, "GBPUSD");

// 移除項目
stringRegistry.Unregister(id);
```

### 使用整數鍵

```cpp
// 獲取使用整數作為鍵的註冊器實例
Registry<int, string>* intKeyRegistry = Registry<int, string>::GetInstance("IntKeyRegistry");

// 註冊項目（使用自動生成的鍵）
int id1 = intKeyRegistry.Register("Symbol", "交易品種", "EURUSD");

// 註冊項目（使用指定的鍵）
int customId = 100;
int id2 = intKeyRegistry.Register("TimeFrame", "時間週期", "H1", customId);
```

### 使用枚舉鍵

```cpp
// 獲取使用枚舉作為鍵的註冊器實例
Registry<ENUM_TIMEFRAMES, double>* timeframeRegistry = Registry<ENUM_TIMEFRAMES, double>::GetInstance("TimeframeRegistry");

// 註冊不同時間週期的移動平均線週期
timeframeRegistry.Register("MA Period H1", "H1 時間週期的移動平均線週期", 50.0, PERIOD_H1);
timeframeRegistry.Register("MA Period H4", "H4 時間週期的移動平均線週期", 100.0, PERIOD_H4);

// 獲取註冊的值
double maPeriodH1 = timeframeRegistry.GetValue(PERIOD_H1);
```

### 註冊對象

```cpp
// 獲取對象註冊器實例
Registry<string, CObject*>* objectRegistry = Registry<string, CObject*>::GetInstance("ObjectRegistry");

// 創建並註冊對象
CArrayString* array = new CArrayString();
array.Add("Item1");
array.Add("Item2");

string id = objectRegistry.Register("StringArray", "字符串數組", array);

// 獲取註冊的對象
CArrayString* retrievedArray = dynamic_cast<CArrayString*>(objectRegistry.GetValue(id));
```

### 遍歷註冊項目

```cpp
// 獲取所有註冊的ID
Key ids[];
registry.GetAllIds(ids);

// 遍歷所有項目
for(int i = 0; i < ArraySize(ids); i++)
{
    RegistryItem<Val>* item = registry.GetItem(ids[i]);
    if(item != NULL)
    {
        // 處理項目
    }
}

// 使用回調函數遍歷
registry.ForEach(MyCallbackFunction);
```

## 注意事項

- 對於指針類型（如 `CObject*`），註冊器會在清空或析構時自動刪除註冊的對象
- 註冊器使用單例模式，每種類型只有一個實例
- 註冊項目的 ID 可以由註冊器自動生成，也可以由用戶指定
- 支持多種類型的鍵，包括 string、int、enum 等
