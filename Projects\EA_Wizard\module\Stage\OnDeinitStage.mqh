#property strict

#include "Enum.mqh"
#include "StageGroup.mqh"
#include "StageManager.mqh"
#include "../Util/Util.mqh"

// OnDeinit 階段群組已移至 StageGroup/OnDeinitStageGroup.mqh

//+------------------------------------------------------------------+
//| 清理階段 - 用於清理資源和註冊器                                 |
//+------------------------------------------------------------------+
class CleanupStage : public OnDeinitStageGroup {
private:
    static OnDeinitStageGroup* s_instance;  // 單例實例
    static OnDeinitStageGroup* GetInstanceInternal() {
        static CleanupStage* instance = NULL;
        if(instance == NULL) {
            instance = new CleanupStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    CleanupStage() : OnDeinitStageGroup("CleanupStage") {}

public:
    // 獲取單例實例
    static OnDeinitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自動註冊到管理器
            IPipelineManager<bool, int>* manager = OnDeinitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("清理階段註冊成功");
            } else {
                Print("警告: 清理階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(int in = 0) override {
        // 先執行父類的 Execute 方法
        bool result = OnDeinitStageGroup::Execute(in);

        // 清理所有註冊器
        // 注意：在實際應用中，您可能需要獲取特定的註冊器實例並清理它
        // 這裡僅作為示例

        // 刪除所有對象
        ObjectsDeleteAll(0, -1, -1);

        Print("清理階段完成");
        return true;
    }
};

// 初始化靜態成員
OnDeinitStageGroup* CleanupStage::s_instance = NULL;

//+------------------------------------------------------------------+
//| 日誌記錄階段 - 用於記錄EA停止的原因和賬戶信息                    |
//+------------------------------------------------------------------+
class OnDeinitLoggingStage : public OnDeinitStageGroup {
private:
    static OnDeinitStageGroup* s_instance;  // 單例實例
    static OnDeinitStageGroup* GetInstanceInternal() {
        static OnDeinitLoggingStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnDeinitLoggingStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    OnDeinitLoggingStage() : OnDeinitStageGroup("OnDeinitLoggingStage") {}

public:
    // 獲取單例實例
    static OnDeinitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自動註冊到管理器
            IPipelineManager<bool, int>* manager = OnDeinitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("日誌記錄階段註冊成功");
            } else {
                Print("警告: 日誌記錄階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    bool Execute(int in = 0) override {
        // 先執行父類的 Execute 方法
        bool result = OnDeinitStageGroup::Execute(in);

        // 獲取 EA 停止的原因
        string reason = "";

        switch(in) {
            case REASON_PROGRAM:
                reason = "程序停止";
                break;
            case REASON_REMOVE:
                reason = "EA 從圖表中移除";
                break;
            case REASON_RECOMPILE:
                reason = "EA 重新編譯";
                break;
            case REASON_CHARTCHANGE:
                reason = "圖表交易品種或週期改變";
                break;
            case REASON_CHARTCLOSE:
                reason = "圖表關閉";
                break;
            case REASON_PARAMETERS:
                reason = "輸入參數改變";
                break;
            case REASON_ACCOUNT:
                reason = "另一個賬戶啟用";
                break;
            case REASON_TEMPLATE:
                reason = "應用新模板";
                break;
            case REASON_INITFAILED:
                reason = "OnInit() 處理失敗";
                break;
            case REASON_CLOSE:
                reason = "終端關閉";
                break;
            default:
                reason = "未知原因";
                break;
        }

        // 記錄 EA 停止的原因
        Print("EA 停止，原因: ", reason, " (代碼: ", in, ")");

        // 記錄賬戶信息
        Print("賬戶餘額: ", AccountBalance());
        Print("賬戶淨值: ", AccountEquity());
        Print("賬戶利潤: ", AccountProfit());

        return true;
    }
};

// 初始化靜態成員
OnDeinitStageGroup* OnDeinitLoggingStage::s_instance = NULL;

class OnDeinitStageMapper
{
private:
    static OnDeinitStageMapper* s_instance;
    Map<int, OnDeinitStageGroupFunc>* m_map;

    OnDeinitStageMapper()
    {
        m_map = new HashMap<int, OnDeinitStageGroupFunc>();
        InitializeMap();
    }

    ~OnDeinitStageMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)ONDEINIT_STAGE_CLEANUP, CleanupStage::GetInstance);
        m_map.set((int)ONDEINIT_STAGE_LOGGING, OnDeinitLoggingStage::GetInstance);
        // 添加其他 OnDeinit 階段...
    }

public:
    static OnDeinitStageMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new OnDeinitStageMapper();
        }
        return s_instance;
    }

    OnDeinitStageGroupFunc GetStageFunction(ENUM_ONDEINIT_STAGE stage)
    {
        return m_map.get((int)stage, NULL);
    }
};

OnDeinitStageMapper* OnDeinitStageMapper::s_instance = NULL;
