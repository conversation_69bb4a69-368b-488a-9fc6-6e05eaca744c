#property strict

#include "../IValidator.mqh"
#include "../ValidationConfig.mqh"

//+------------------------------------------------------------------+
//| 必填驗證器                                                         |
//| 用於驗證字段是否為空                                                |
//+------------------------------------------------------------------+
class CRequiredValidator : public CBaseValidator
{
private:
    string m_value;    // 要驗證的值
    string m_field;    // 欄位名稱

public:
    // 建構函數
    CRequiredValidator(string value, const string field)
        : CBaseValidator(VALIDATOR_NAME_REQUIRED, VALIDATOR_TYPE_REQUIRED)
        , m_value(value)
        , m_field(field)
    {}

    // 執行驗證
    virtual CValidationResult* Validate() override
    {
        CValidationResult* result = new CValidationResult();
        
        // 檢查值是否為空
        if(StringLen(m_value) == 0)
        {
            result.SetInvalid(
                m_field, 
                StringFormat(VALIDATOR_MSG_REQUIRED, m_field),
                VALIDATOR_SOURCE_DEFAULT,
                VALIDATOR_ERROR_REQUIRED
            );
        }
        
        return result;
    }
    
    // 設置要驗證的值
    void SetValue(const string value)
    {
        m_value = value;
    }
    
    // 獲取要驗證的值
    string GetValue() const
    {
        return m_value;
    }
    
    // 設置欄位名稱
    void SetField(const string field)
    {
        m_field = field;
    }
    
    // 獲取欄位名稱
    string GetField() const
    {
        return m_field;
    }
};
