//+------------------------------------------------------------------+
//|                                               StringRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Registry.mqh"

//+------------------------------------------------------------------+
//| 字符串註冊器類 - 用於註冊和管理字符串類型的對象                    |
//+------------------------------------------------------------------+
template <typename Val>
class StringRegistry : public Registry<string, Val>
{
protected:
    string m_keyPrefix;                     // 鍵的字頭

public:
    // 公開構造函數（移除單例模式）
    StringRegistry(const string name = "StringRegistry", const int maxItems = 100)
        : Registry<string, Val>(name, maxItems)
    {
        m_keyPrefix = "SR"; // 預設字頭
    }

    ~StringRegistry() {
    }

    // 生成唯一ID
    string GenerateKey() override
    {
        // 使用 GetAddress 和 MurmurHash3_x86_32 生成唯一的鍵
        long address = GetAddress(&this);
        string addressStr = IntegerToString(address);
        uint seed = (uint)TimeCurrent(); // 使用當前時間作為種子
        uint hash = MurmurHash3_x86_32(addressStr, seed);

        // 返回字頭加哈希值
        return this.m_keyPrefix + "_" + IntegerToString(hash);
    }

    // 設置鍵的字頭
    void SetKeyPrefix(const string prefix)
    {
        this.m_keyPrefix = prefix;
    }

    // 獲取鍵的字頭
    string GetKeyPrefix() const
    {
        return this.m_keyPrefix;
    }

    // 將鍵轉換為字符串
    string KeyToString(const string id) override
    {
        return id; // 字符串鍵直接返回
    }

    // 註冊新項目
    bool Register(const string name, const string description, Val value, string key = NULL) override
    {
        // 檢查是否達到最大項目數量
        if(this.m_items.size() >= this.m_maxItems)
        {
            Print("註冊失敗：已達到最大項目數量 ", this.m_maxItems);
            return false;
        }

        // 生成唯一ID或使用提供的鍵
        string id;
        if(key == NULL || key == "")
        {
            id = GenerateKey();
        }
        else
        {
            id = key;
            // 檢查鍵是否已存在
            if(this.m_items.contains(id))
            {
                Print("註冊失敗：鍵已存在");
                return false;
            }
        }

        // 創建新項目
        IRegistryItem<Val>* item = new RegistryItem<Val>(id, name, description, value);

        // 添加到哈希表
        this.m_items.set(id, item);

        // 保存最後註冊的鍵
        this.m_lastRegisteredKey = id;

        Print("註冊成功：", item.ToString());
        return true;
    }

};