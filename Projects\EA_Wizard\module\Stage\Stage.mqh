#property strict

#include "Enum.mqh"
#include "../Pipeline/RegistrablePipeline.mqh"
#include "StageManager.mqh"
#include "StageRegistry.mqh"
#include "OnInitStage.mqh"
#include "OnTickStage.mqh"
#include "OnDeinitStage.mqh"

//+------------------------------------------------------------------+
//| 階段介面 - 定義階段的基本功能                                    |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename Key, typename Val>
interface IStage : public IRegistrablePipeline<Output, Input, Key, Val> {
public:
    bool IsRegistered() const;
};

//+------------------------------------------------------------------+
//| Stage 模板類 - 作為所有階段的基類                                |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename Key, typename Val>
class Stage : public IStage<Output, Input, Key, Val> {
private:
    RegistrablePipeline<Output, Input, Key, Val> m_pipeline;

    bool m_registered;                         // 是否已註冊到管理器

public:
    // 建構函數 - 自動註冊到管理器
    Stage(IStageGroup<Output, Input>* group, Registry<Key, Val>* registry)
    : m_pipeline(registry) {
        // 將自身添加到管理器中
        m_registered = group.AddPipeline(&this);
        if(m_registered) {
            Print("階段註冊成功");
        } else {
            Print("警告: 階段註冊失敗");
        }
    }

    // 建構函數 - 不註冊到管理器
    Stage(Registry<Key, Val>* registry)
    : m_pipeline(registry) {
        // 不註冊到管理器
        m_registered = false;
    }

    // 解構函數
    ~Stage() {}

    bool IsError() override {
        return m_pipeline.IsError();
    }

    string LastError() override {
        return m_pipeline.LastError();
    }

    bool Register(const string name, const string description, Val value, Key key = NULL) override {
        return m_pipeline.Register(name, description, value, key);
    }

    Registry<Key, Val>* GetRegistry() const override {
        return m_pipeline.GetRegistry();
    }

    // 獲取是否已註冊到管理器
    bool IsRegistered() const { return m_registered; }

    // 執行階段（由子類實現）
    virtual Output Execute(Input in) = 0;


};

//+------------------------------------------------------------------+
//| OnInit 階段基類 - 用於實現 EA 初始化階段                         |
//+------------------------------------------------------------------+
class OnInitStage : public Stage<ENUM_INIT_RETCODE, void*, string, void*> {
public:
    // 建構函數 - 自動註冊到管理器
    OnInitStage(ENUM_ONINIT_STAGE stage = ONINIT_STAGE_START, StageRegistry* registry=NULL)
    : Stage<ENUM_INIT_RETCODE, void*, string, void*>(
        OnInitStageMapper::GetInstance().GetStageFunction(stage)(),
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 建構函數 - 不註冊到管理器
    OnInitStage(StageRegistry* registry=NULL)
    : Stage<ENUM_INIT_RETCODE, void*, string, void*>(
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 執行階段（由子類實現）
    virtual ENUM_INIT_RETCODE Execute(void* in = NULL) = 0;
};

//+------------------------------------------------------------------+
//| OnTick 階段基類 - 用於實現 EA 周期性執行階段                     |
//+------------------------------------------------------------------+
class OnTickStage : public Stage<bool, void*, string, void*> {
public:
    // 建構函數 - 自動註冊到管理器
    OnTickStage(ENUM_ONTICK_STAGE stage = ONTICK_STAGE_START, StageRegistry* registry=NULL)
    : Stage<bool, void*, string, void*>(
        OnTickStageMapper::GetInstance().GetStageFunction(stage)(),
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 建構函數 - 不註冊到管理器
    OnTickStage(StageRegistry* registry=NULL)
    : Stage<bool, void*, string, void*>(
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 執行階段（由子類實現）
    virtual bool Execute(void* in = NULL) = 0;
};

//+------------------------------------------------------------------+
//| OnDeinit 階段基類 - 用於實現 EA 終止階段                         |
//+------------------------------------------------------------------+
class OnDeinitStage : public Stage<bool, int, string, void*> {
public:
    // 建構函數 - 自動註冊到管理器
    OnDeinitStage(ENUM_ONDEINIT_STAGE stage = ONDEINIT_STAGE_CLEANUP, StageRegistry* registry=NULL)
    : Stage<bool, int, string, void*>(
        OnDeinitStageMapper::GetInstance().GetStageFunction(stage)(),
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 建構函數 - 不註冊到管理器
    OnDeinitStage(StageRegistry* registry=NULL)
    : Stage<bool, int, string, void*>(
        registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
    ) {
    }

    // 執行階段（由子類實現）
    virtual bool Execute(int in = 0) = 0;
};

// //+------------------------------------------------------------------+
// //| Stage 模板類 - 作為所有階段的基類                                |
// //+------------------------------------------------------------------+
// template <typename Output, typename Input, typename Key, typename Val>
// class Stage : public RegistrablePipeline<Output, Input, Key, Val> {
// private:
//     bool m_registered;                         // 是否已註冊到管理器

// public:
//     // 建構函數 - 自動註冊到管理器
//     Stage(PipelineManager<Output, Input>* manager, Registry<Key, Val>* registry)
//     : RegistrablePipeline<Output, Input, Key, Val>(registry) {
//         // 將自身添加到管理器中
//         m_registered = manager.AddPipeline(&this);
//         if(m_registered) {
//             Print("階段註冊成功");
//         } else {
//             Print("警告: 階段註冊失敗");
//         }
//     }

//     // 建構函數 - 不註冊到管理器
//     Stage(Registry<Key, Val>* registry)
//     : RegistrablePipeline<Output, Input, Key, Val>(registry) {
//         // 不註冊到管理器
//         m_registered = false;
//     }

//     // 解構函數
//     ~Stage() {}

//     // 獲取是否已註冊到管理器
//     bool IsRegistered() const { return m_registered; }

//     // 執行階段（由子類實現）
//     virtual Output Execute(Input in) = 0;


// };

// //+------------------------------------------------------------------+
// //| OnInit 階段基類 - 用於實現 EA 初始化階段                         |
// //+------------------------------------------------------------------+
// class OnInitStage : public Stage<ENUM_INIT_RETCODE, void*, string, void*> {
// public:
//     // 建構函數 - 自動註冊到管理器
//     OnInitStage(PipelineManager<ENUM_INIT_RETCODE, void*>* manager=NULL, StageRegistry* registry=NULL)
//     : Stage<ENUM_INIT_RETCODE, void*, string, void*>(
//         manager ? manager : OnInitStageManager::GetInstance(),
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 建構函數 - 不註冊到管理器
//     OnInitStage(StageRegistry* registry=NULL)
//     : Stage<ENUM_INIT_RETCODE, void*, string, void*>(
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 執行階段（由子類實現）
//     virtual ENUM_INIT_RETCODE Execute(void* in = NULL) = 0;
// };

// //+------------------------------------------------------------------+
// //| OnTick 階段基類 - 用於實現 EA 周期性執行階段                     |
// //+------------------------------------------------------------------+
// class OnTickStage : public Stage<bool, void*, string, void*> {
// public:
//     // 建構函數 - 自動註冊到管理器
//     OnTickStage(PipelineManager<bool, void*>* manager=NULL, StageRegistry* registry=NULL)
//     : Stage<bool, void*, string, void*>(
//         manager ? manager : OnTickStageManager::GetInstance(),
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 建構函數 - 不註冊到管理器
//     OnTickStage(StageRegistry* registry=NULL)
//     : Stage<bool, void*, string, void*>(
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 執行階段（由子類實現）
//     virtual bool Execute(void* in = NULL) = 0;
// };

// //+------------------------------------------------------------------+
// //| OnDeinit 階段基類 - 用於實現 EA 終止階段                         |
// //+------------------------------------------------------------------+
// class OnDeinitStage : public Stage<bool, int, string, void*> {
// public:
//     // 建構函數 - 自動註冊到管理器
//     OnDeinitStage(PipelineManager<bool, int>* manager=NULL, StageRegistry* registry=NULL)
//     : Stage<bool, int, string, void*>(
//         manager ? manager : OnDeinitStageManager::GetInstance(),
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 建構函數 - 不註冊到管理器
//     OnDeinitStage(StageRegistry* registry=NULL)
//     : Stage<bool, int, string, void*>(
//         registry ? registry : StageRegistry::GetInstance("StageRegistry", 100)
//     ) {
//     }

//     // 執行階段（由子類實現）
//     virtual bool Execute(int in = 0) = 0;
// };

class SampleStage1 : public OnInitStage {
public:
    SampleStage1() : OnInitStage(ONINIT_STAGE_START) {}

    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        Print("執行階段");
        return INIT_SUCCEEDED;
    }
};
