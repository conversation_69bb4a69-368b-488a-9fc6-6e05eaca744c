#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// 市場數據更新階段
class MarketDataStage : public OnTickPipeline
{
private:
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;
    
public:
    // 建構函數
    MarketDataStage(string symbol = NULL, ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT)
        : m_symbol(symbol == NULL ? Symbol() : symbol),
          m_timeframe(timeframe == PERIOD_CURRENT ? Period() : timeframe)
    {
    }
    
    bool Execute(void* in = NULL) override
    {
        // 更新市場數據
        RefreshRates(); // 刷新市場報價
        
        // 檢查市場數據是否有效
        if(MarketInfo(m_symbol, MODE_BID) <= 0 || MarketInfo(m_symbol, MODE_ASK) <= 0)
        {
            Print("市場數據無效: ", m_symbol);
            return false;
        }
        
        // 可以在這裡添加更多的市場數據處理邏輯
        // 例如: 獲取最新的K線數據、計算技術指標等
        
        Print("市場數據更新成功: ", m_symbol, ", 時間週期: ", EnumToString(m_timeframe));
        return true;
    }
};
