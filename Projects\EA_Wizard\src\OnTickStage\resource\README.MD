# EA_Wizard 交易流水線階段

以下是已實現的交易流水線階段：

1. **OnTick 開始 (OnTickStartStage)**

   - 檢查交易是否允許
   - 檢查是否連接到交易伺服器

2. **市場數據更新 (MarketDataStage)**

   - 更新市場報價
   - 檢查市場數據有效性

3. **持倉狀態檢查 (PositionCheckStage)**

   - 掃描當前訂單
   - 統計買入和賣出訂單數量及交易量
   - 檢查是否有持倉

4. **信號檢查 (SignalCheckStage)**

   - 檢查是否有交易信號
   - 計算信號強度

5. **風險控制檢查 (RiskCheckStage)**

   - 檢查帳戶餘額和權益
   - 檢查保證金水平
   - 檢查持倉數量
   - 檢查風險百分比

6. **交易執行 (TradeExecutionStage)**

   - 根據信號執行交易
   - 設置止損和止盈

7. **日誌記錄和通知 (LoggingStage)**

   - 記錄交易信息
   - 發送警報和通知

8. **OnTick 結束 (OnTickEndStage)**

   - 記錄執行時間
   - 完成 OnTick 處理

## 使用方法

```mq4
// 創建交易流水線處理器
TickPipelineProcessor* processor = new TickPipelineProcessor(12345, Symbol());

// 執行流水線
bool result = processor.Execute();

// 處理結果
if(!result)
{
    Print("交易流水線執行失敗");
}
else
{
    Print("交易流水線執行成功");
}
```

## 流程圖

```
OnTick 開始
    |
    v
讀取行情/指標
    |
    v
檢查持倉狀態
    |
    v
判斷是否有交易信號
    |     \
    |      \
無信號   有信號
    |        |
    |        v
    |     風控檢查
    |        |
    |        v
    |     執行下單/平倉
    |        |
    |        v
    |     紀錄/通知
    |       /
    v      /
    結束
```
