//+------------------------------------------------------------------+
//| Module: Lang/Indicator.mqh                                       |
//| This file is part of the mql4-lib project:                       |
//|     https://github.com/dingmaotu/mql4-lib                        |
//|                                                                  |
//| Copyright 2015-2016 Li Ding <<EMAIL>>                  |
//|                                                                  |
//| Licensed under the Apache License, Version 2.0 (the "License");  |
//| you may not use this file except in compliance with the License. |
//| You may obtain a copy of the License at                          |
//|                                                                  |
//|     http://www.apache.org/licenses/LICENSE-2.0                   |
//|                                                                  |
//| Unless required by applicable law or agreed to in writing,       |
//| software distributed under the License is distributed on an      |
//| "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,     |
//| either express or implied.                                       |
//| See the License for the specific language governing permissions  |
//| and limitations under the License.                               |
//+------------------------------------------------------------------+
#property strict

#include "EventApp.mqh"

#define DECLARE_INDICATOR(AppClass,Boolean) \
DECLARE_EVENT_APP(AppClass,Boolean)\
int OnCalculate(const int rates_total,const int prev_calculated,const datetime &time[],const double &open[],const double &high[],const double &low[],const double &close[],const long &tickVolume[],const long &volume[],const int &spread[])\
  {return dynamic_cast<Indicator*>(App::Global).main(rates_total,prev_calculated,time,open,high,low,close,tickVolume,volume,spread);}
//+------------------------------------------------------------------+
//| Base class for a MQL Indicator                                   |
//+------------------------------------------------------------------+
class Indicator: public EventApp
  {
public:
   virtual int       main(const int total,
                          const int prev,
                          const datetime &time[],
                          const double &open[],
                          const double &high[],
                          const double &low[],
                          const double &close[],
                          const long &tickVolume[],
                          const long &volume[],
                          const int &spread[])
     {return total;}
  };
//+------------------------------------------------------------------+
