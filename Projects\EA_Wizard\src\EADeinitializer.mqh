#property strict

#include "../module/EAStage/StageRegistry.mqh"
#include "OnDeinitStage/OnDeinitStage.mqh"

bool EADeinitializer(const int reason){
    // 獲取註冊器實例
    StringRegistry<void*>* registry = new StringRegistry<void*>();

    // 創建並執行清理流水線處理器
    DeinitPipelineProcessor* processor = new DeinitPipelineProcessor();
    bool result = processor.Execute(reason);

    // 處理執行結果
    if(!result)
    {
        Print("EA清理失敗，清理流水線執行失敗");
        delete processor;
        return false;
    }

    Print("EA清理成功");
    delete processor;
    return true;
}
