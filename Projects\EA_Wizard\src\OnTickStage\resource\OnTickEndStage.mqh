#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// OnTick 結束階段
class OnTickEndStage : public OnTickPipeline
{
private:
    datetime m_last_tick_time;
    
public:
    // 建構函數
    OnTickEndStage()
        : m_last_tick_time(0)
    {
    }
    
    bool Execute(void* in = NULL) override
    {
        // 獲取當前時間
        datetime current_time = TimeCurrent();
        
        // 計算距離上次 Tick 的時間間隔（秒）
        int time_diff = (int)(current_time - m_last_tick_time);
        
        // 更新上次 Tick 時間
        m_last_tick_time = current_time;
        
        // 輸出 OnTick 結束信息
        Print("OnTick 執行結束 - 時間: ", TimeToString(current_time, TIME_DATE|TIME_SECONDS), 
              ", 距離上次 Tick: ", time_diff, " 秒");
        
        return true;
    }
};
