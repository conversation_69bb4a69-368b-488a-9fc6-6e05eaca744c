#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/mql4-lib-master/Trade/OrderManager.mqh"
#include "../../module/Util/ErrorHandler.mqh"

const int DEFAULT_OM_MAGIC_NUMBER = 12345;
const int DEFAULT_OM_SLIPPAGE = 3;
const int DEFAULT_OM_RETRY = 10;
const color DEFAULT_OM_BUY_COLOR = clrBlue;
const color DEFAULT_OM_SELL_COLOR = clrRed;
const color DEFAULT_OM_CLOSE_COLOR = clrWhite;

// 初始化訂單管理器階段
class InitOrderManager : public OnInitStage
{
public:
    InitOrderManager() : OnInitStage(ONINIT_STAGE_VARIABLE_INIT){}
    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化訂單管理器階段");

        ErrorHandler* error_handler = dynamic_cast<ErrorHandler*>(GetRegistry().GetValue("ErrorHandler", NULL));
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            return INIT_FAILED;
        }

        order_manager_cfg.magic_number = DEFAULT_OM_MAGIC_NUMBER;
        order_manager_cfg.slippage = DEFAULT_OM_SLIPPAGE;
        order_manager_cfg.retry = DEFAULT_OM_RETRY;
        order_manager_cfg.buy_color = DEFAULT_OM_BUY_COLOR;
        order_manager_cfg.sell_color = DEFAULT_OM_SELL_COLOR;
        order_manager_cfg.close_color = DEFAULT_OM_CLOSE_COLOR;

        if(!order_manager_cfg.ValidateData()) {
            error_handler.HandleError("訂單管理器配置數據驗證失敗");
            return INIT_FAILED;
        }

        OrderManager* order_manager = new OrderManager(Symbol());
        if(!Register("OrderManager", "訂單管理器", order_manager, "OrderManager"))
        {
            error_handler.HandleError("訂單管理器註冊失敗");
            return INIT_FAILED;
        }

        order_manager.setSlippage(order_manager_cfg.slippage);
        order_manager.setRetry(order_manager_cfg.retry);
        order_manager.setMagic(order_manager_cfg.magic_number);
        order_manager.setBuyColor(order_manager_cfg.buy_color);
        order_manager.setSellColor(order_manager_cfg.sell_color);
        order_manager.setCloseColor(order_manager_cfg.close_color);

        if(!this.IsRegistered()) {
            error_handler.HandleError("註冊失敗");
            return INIT_FAILED;
        }

        return INIT_SUCCEEDED;
    }
}init_order_manager_stage;

struct OrderManagerConfig {
    int magic_number;
    int slippage;
    int retry;
    color buy_color;
    color sell_color;
    color close_color;

    bool ValidateData()
  {
   if(this.magic_number <= 0) return false;
   if(this.slippage < 0) return false;
   if(this.retry < 0) return false;
   if(this.buy_color == CLR_NONE) return false;
   if(this.sell_color == CLR_NONE) return false;
   if(this.close_color == CLR_NONE) return false;
   return true;
  }

    // Serialize struct to JSON string
    string Serialize() {
        string json = "{";
        json += "\"magic_number\":" + IntegerToString(this.magic_number) + ",";
        json += "\"slippage\":" + IntegerToString(this.slippage) + ",";
        json += "\"retry\":" + IntegerToString(this.retry) + ",";
        json += "\"buy_color\":" + IntegerToString(this.buy_color) + ",";
        json += "\"sell_color\":" + IntegerToString(this.sell_color) + ",";
        json += "\"close_color\":" + IntegerToString(this.close_color);
        json += "}";
        return json;
    }

    // Deserialize JSON string to struct (assumes correct format)
    void Deserialize(string json) {
        this.magic_number = StrToInteger(StringGetBetween(json, "\"magic_number\":", ","));
        this.slippage     = StrToInteger(StringGetBetween(json, "\"slippage\":", ","));
        this.retry        = StrToInteger(StringGetBetween(json, "\"retry\":", ","));
        this.buy_color    = StringToColor(StringGetBetween(json, "\"buy_color\":", ","));
        this.sell_color   = StringToColor(StringGetBetween(json, "\"sell_color\":", ","));
        this.close_color  = StringToColor(StringGetBetween(json, "\"close_color\":", "}"));
    }

    // Helper function to extract substring between two delimiters
    string StringGetBetween(string text, string start, string end) {
        int pos1 = StringFind(text, start);
        if(pos1 < 0) return "";
        pos1 += StringLen(start);
        int pos2 = StringFind(text, end, pos1);
        if(pos2 < 0) return "";
        return StringSubstr(text, pos1, pos2 - pos1);
    }
}order_manager_cfg = {12345,3,10,clrBlue,clrRed,clrWhite};
