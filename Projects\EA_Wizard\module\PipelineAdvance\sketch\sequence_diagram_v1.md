# PipelineAdvance 模組序列圖 (v1)

本文檔展示了 PipelineAdvance 模組的執行序列，包括流水線的創建、配置和執行過程。

## 基本流水線執行序列

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Main as 主流水線
    participant Child1 as 子流水線1
    participant Child2 as 子流水線2
    participant Child3 as 子流水線3

    Client->>Main: 創建主流水線(name, type, maxItems)
    Client->>Child1: 創建子流水線1
    Client->>Child2: 創建子流水線2
    Client->>Child3: 創建子流水線3
    
    Client->>Main: Add(Child1)
    Main-->>Client: 返回添加結果
    Client->>Main: Add(Child2)
    Main-->>Client: 返回添加結果
    Client->>Main: Add(Child3)
    Main-->>Client: 返回添加結果
    
    Client->>Main: GetCount()
    Main-->>Client: 返回子流水線數量
    Client->>Main: GetMaxItems()
    Main-->>Client: 返回最大子流水線數量
    
    Client->>Main: Execute()
    Main->>Main: 檢查是否已執行
    Main->>Main: 檢查子流水線是否都未執行
    Main->>Child1: Execute()
    Child1->>Child1: 執行邏輯
    Child1-->>Main: 完成執行
    Main->>Child1: GetResult()
    Child1-->>Main: 返回結果
    
    alt 子流水線1執行成功
        Main->>Child2: Execute()
        Child2->>Child2: 執行邏輯
        Child2-->>Main: 完成執行
        Main->>Child2: GetResult()
        Child2-->>Main: 返回結果
        
        alt 子流水線2執行成功
            Main->>Child3: Execute()
            Child3->>Child3: 執行邏輯
            Child3-->>Main: 完成執行
            Main->>Child3: GetResult()
            Child3-->>Main: 返回結果
            Main->>Main: 設置成功結果
        else 子流水線2執行失敗
            Main->>Main: 設置失敗結果
        end
    else 子流水線1執行失敗
        Main->>Main: 設置失敗結果
    end
    
    Main-->>Client: 完成執行
    Client->>Main: GetResult()
    Main-->>Client: 返回結果
    
    Client->>Main: Restore()
    Main->>Main: 重置結果
    Main->>Child1: Restore()
    Main->>Child2: Restore()
    Main->>Child3: Restore()
    Main-->>Client: 完成重置
    
    Client->>Main: 釋放資源
```

## 流水線錯誤處理序列

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Main as 主流水線
    participant Child1 as 子流水線1(已執行)
    participant Child2 as 子流水線2
    
    Client->>Main: 創建主流水線
    Client->>Child1: 創建子流水線1
    Client->>Child2: 創建子流水線2
    
    Client->>Main: Add(Child1)
    Main-->>Client: 返回添加結果
    Client->>Main: Add(Child2)
    Main-->>Client: 返回添加結果
    
    Client->>Child1: Execute()
    Child1->>Child1: 執行邏輯
    Child1->>Child1: 設置已執行狀態
    
    Client->>Main: Execute()
    Main->>Main: 檢查是否已執行
    Main->>Main: 檢查子流水線是否都未執行
    Main->>Child1: IsExecuted()
    Child1-->>Main: 返回 true
    Main->>Main: 設置失敗結果(子流水線已執行)
    Main-->>Client: 完成執行
    
    Client->>Main: GetResult()
    Main-->>Client: 返回失敗結果
    
    Client->>Main: Restore()
    Main->>Main: 重置結果
    Main->>Child1: Restore()
    Main->>Child2: Restore()
    Main-->>Client: 完成重置
    
    Client->>Main: Execute()
    Main->>Main: 檢查是否已執行
    Main->>Main: 檢查子流水線是否都未執行
    Main->>Child1: IsExecuted()
    Child1-->>Main: 返回 false
    Main->>Child2: IsExecuted()
    Child2-->>Main: 返回 false
    Main->>Child1: Execute()
    Child1->>Child1: 執行邏輯
    Child1-->>Main: 完成執行
    Main->>Child1: GetResult()
    Child1-->>Main: 返回結果
    Main->>Child2: Execute()
    Child2->>Child2: 執行邏輯
    Child2-->>Main: 完成執行
    Main->>Child2: GetResult()
    Child2-->>Main: 返回結果
    Main->>Main: 設置成功結果
    Main-->>Client: 完成執行
    
    Client->>Main: GetResult()
    Main-->>Client: 返回成功結果
```

## 流水線容量限制序列

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Main as 主流水線(maxItems=2)
    participant Child1 as 子流水線1
    participant Child2 as 子流水線2
    participant Child3 as 子流水線3
    
    Client->>Main: 創建主流水線(maxItems=2)
    Client->>Child1: 創建子流水線1
    Client->>Child2: 創建子流水線2
    Client->>Child3: 創建子流水線3
    
    Client->>Main: Add(Child1)
    Main->>Main: 檢查是否超過最大數量
    Main-->>Client: 返回添加成功結果
    
    Client->>Main: Add(Child2)
    Main->>Main: 檢查是否超過最大數量
    Main-->>Client: 返回添加成功結果
    
    Client->>Main: Add(Child3)
    Main->>Main: 檢查是否超過最大數量
    Main-->>Client: 返回添加失敗結果(超過最大數量)
    
    Client->>Main: GetCount()
    Main-->>Client: 返回 2
    
    Client->>Main: GetMaxItems()
    Main-->>Client: 返回 2
    
    Client->>Main: Remove(Child2)
    Main-->>Client: 返回移除成功結果
    
    Client->>Main: Add(Child3)
    Main->>Main: 檢查是否超過最大數量
    Main-->>Client: 返回添加成功結果
```
