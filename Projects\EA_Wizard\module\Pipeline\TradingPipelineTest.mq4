//+------------------------------------------------------------------+
//|                                          TradingPipelineTest.mq4 |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    // 創建交易流水線管理器
    TradingPipelineManager* manager = new TradingPipelineManager();

    // 設置註冊器的鍵字頭
    manager.GetRegistry().SetKeyPrefix("Trading");

    // 獲取管理器ID
    string managerId = manager.GetId();
    Print("管理器ID: ", managerId);

    // 創建交易流水線階段
    EntryPipeline* entryPipeline = new EntryPipeline(manager);
    ExitPipeline* exitPipeline = new ExitPipeline(manager);
    TradeManagementPipeline* tradeManagementPipeline = new TradeManagementPipeline(manager);

    // 添加階段到流水線
    manager.AddPipeline(entryPipeline);
    manager.AddPipeline(exitPipeline);
    manager.AddPipeline(tradeManagementPipeline);

    // 執行交易流水線
    bool result = manager.Execute();

    // 輸出結果
    Print("交易流水線執行結果: ", (result ? "成功" : "失敗"));

    // 獲取註冊器中的所有項目
    string ids[];
    manager.GetRegistry().GetAllIds(ids);

    Print("註冊器中的項目數量: ", ArraySize(ids));

    // 遍歷所有項目
    for(int i = 0; i < ArraySize(ids); i++)
    {
        IRegistryItem<PipelineManager<bool, void*>*>* item = manager.GetRegistry().GetItem(ids[i]);
        if(item != NULL)
        {
            Print("項目 ID: ", ids[i], ", 名稱: ", item.GetName());
        }
    }

    // 清理資源
    delete manager; // 這將自動刪除所有流水線階段

    Print("測試完成！");
}
//+------------------------------------------------------------------+
