//+------------------------------------------------------------------+
//|                                           VoidPointerRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "PointerRegistry.mqh"

//+------------------------------------------------------------------+
//| void* 指針註冊器類 - 用於註冊和管理 void* 類型的對象            |
//+------------------------------------------------------------------+
class VoidPointerRegistry : public PointerRegistry<void*>
{
private:
    static VoidPointerRegistry* m_instance;  // 單例實例

    // 私有構造函數（單例模式）
    VoidPointerRegistry(const string name = "VoidPointerRegistry", const int maxItems = 100)
        : PointerRegistry<void*>(name, maxItems)
    {
        SetKeyPrefix("VPR"); // 設置 void* 註冊器的字頭
    }

public:
    // 獲取單例實例
    static VoidPointerRegistry* GetInstance(const string name = "VoidPointerRegistry", const int maxItems = 100)
    {
        if(m_instance == NULL)
        {
            m_instance = new VoidPointerRegistry(name, maxItems);
        }
        return m_instance;
    }
};

// 初始化靜態成員
VoidPointerRegistry* VoidPointerRegistry::m_instance = NULL;
