#property strict

#include "../../module/Registry/Registry.mqh"

//+------------------------------------------------------------------+
//| 可註冊流水線介面 - 定義可註冊流水線的基本功能                    |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename Key, typename Val>
interface IRegistrablePipeline : public IPipeline<Output, Input>
{
public:
    virtual bool Register(const string name, const string description, Val value, Key key = NULL) = 0;
    virtual Registry<Key, Val>* GetRegistry() const = 0;
};

//+------------------------------------------------------------------+
//| 可註冊流水線基類 - 將流水線階段與註冊器結合                  |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename Key, typename Val>
class RegistrablePipeline : public IRegistrablePipeline<Output, Input, Key, Val>
{
private:
    Registry<Key, Val>* m_registry;  // 註冊器指標
    bool m_is_error;
    string m_last_error;

public:
    // 建構函數 - 接受註冊器指標
    RegistrablePipeline(Registry<Key, Val>* registry)
    : m_registry(registry) {
        if(registry == NULL) {
            Print("警告: 註冊器指標為空");
        }
    }

    // 解構函數
    ~RegistrablePipeline() {}

    bool IsError() override
    {
        return m_is_error;
    }

    string LastError() override
    {
        return m_last_error;
    }

    // 註冊數據到註冊器
    bool Register(const string name, const string description, Val value, Key key = NULL)
    {
        if(m_registry == NULL) {
            m_is_error = true;
            m_last_error = "註冊器指標為空，無法註冊";
            Print("註冊器指標為空，無法註冊");
            return false;
        }
        return m_registry.Register(name, description, value, key);
    }

    // 獲取註冊器
    Registry<Key, Val>* GetRegistry() const { return m_registry; }

    Output Execute(Input in) override
    {
        return NULL;
    }
};
