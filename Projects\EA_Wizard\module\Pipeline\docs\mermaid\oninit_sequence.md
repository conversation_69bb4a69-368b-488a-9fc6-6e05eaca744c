```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as InitPipelineProcessor
    participant Stage1 as OnInitStartStage
    participant Stage2 as ParameterReadStage
    participant Stage3 as VariableInitStage
    participant Stage4 as IndicatorInitStage
    participant Stage5 as OnInitEndStage

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 OnInitStartStage
    Processor->>Stage2: 添加 ParameterReadStage
    Processor->>Stage3: 添加 VariableInitStage
    Processor->>Stage4: 添加 IndicatorInitStage
    Processor->>Stage5: 添加 OnInitEndStage
    EA->>Processor: Execute()
    Processor->>Stage1: Execute(NULL)
    Stage1-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage2: Execute(NULL)
    Stage2-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage3: Execute(NULL)
    Stage3-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage4: Execute(NULL)
    Stage4-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage5: Execute(NULL)
    Stage5-->>Processor: 返回 INIT_SUCCEEDED
    Processor-->>EA: 返回 INIT_SUCCEEDED
    EA->>Processor: 釋放資源
```
