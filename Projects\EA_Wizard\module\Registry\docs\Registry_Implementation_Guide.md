# Registry 模組實現指南

## 1. 核心類別實現詳解

### 1.1 RegistryItem 類

`RegistryItem` 類是註冊器中存儲的基本單元，實現了 `IRegistryItem` 介面：

```cpp
template<typename T>
class RegistryItem : public IRegistryItem<T>
{
private:
    string m_id;           // 項目唯一標識符
    string m_name;         // 項目名稱
    string m_description;  // 項目描述
    T      m_value;        // 項目值
    datetime m_createTime; // 創建時間
    datetime m_updateTime; // 最後更新時間

public:
    // 構造函數
    RegistryItem(const string id, const string name, const string description, T value)
        : m_id(id), m_name(name), m_description(description), m_value(value)
    {
        m_createTime = TimeCurrent();
        m_updateTime = m_createTime;
    }

    // 實現介面方法
    string GetId() const { return m_id; }
    string GetName() const { return m_name; }
    string GetDescription() const { return m_description; }
    T GetValue() const { return m_value; }
    void SetValue(T value) { m_value = value; m_updateTime = TimeCurrent(); }
    datetime GetCreateTime() const { return m_createTime; }
    datetime GetUpdateTime() const { return m_updateTime; }
    string ToString() const { /* 實現省略 */ }
};
```

### 1.2 Registry 抽象基類

`Registry` 是所有註冊器的抽象基類，定義了註冊器的基本功能：

```cpp
template <typename Key, typename Val>
class Registry
{
protected:
    string m_name;                         // 註冊器名稱
    HashMap<Key, IRegistryItem<Val>*>* m_items; // 項目哈希表
    int m_maxItems;                        // 最大項目數量
    Key m_lastRegisteredKey;               // 最後註冊的鍵

public:
    // 構造函數
    Registry(const string name, const int maxItems)
        : m_name(name), m_maxItems(maxItems)
    {
        m_items = new HashMap<Key, IRegistryItem<Val>*>(NULL, true);
    }

    // 析構函數
    ~Registry()
    {
        if(m_items != NULL)
        {
            delete m_items;
            m_items = NULL;
        }
    }

    // 抽象方法（由子類實現）
    virtual bool Register(const string name, const string description, Val value, Key key = NULL) = 0;
    virtual Key GenerateKey() = 0;
    virtual string KeyToString(const Key id) = 0;

    // 具體方法實現
    IRegistryItem<Val>* GetItem(const Key id) { /* 實現省略 */ }
    Val GetValue(const Key id, Val defaultValue = NULL) { /* 實現省略 */ }
    bool UpdateValue(const Key id, Val value) { /* 實現省略 */ }
    bool Unregister(const Key id) { /* 實現省略 */ }
    void Clear() { /* 實現省略 */ }
    int GetCount() const { /* 實現省略 */ }
    // 其他方法...
};
```

### 1.3 StringRegistry 類

`StringRegistry` 是使用字符串作為鍵的具體註冊器實現：

```cpp
template <typename Val>
class StringRegistry : public Registry<string, Val>
{
protected:
    string m_keyPrefix;                     // 鍵的字頭

public:
    // 構造函數
    StringRegistry(const string name = "StringRegistry", const int maxItems = 100)
        : Registry<string, Val>(name, maxItems)
    {
        m_keyPrefix = "SR"; // 預設字頭
    }

    // 生成唯一ID
    string GenerateKey() override
    {
        // 使用 GetAddress 和 MurmurHash3_x86_32 生成唯一的鍵
        long address = GetAddress(&this);
        string addressStr = IntegerToString(address);
        uint seed = (uint)TimeCurrent(); // 使用當前時間作為種子
        uint hash = MurmurHash3_x86_32(addressStr, seed);

        // 返回字頭加哈希值
        return this.m_keyPrefix + "_" + IntegerToString(hash);
    }

    // 設置鍵的字頭
    void SetKeyPrefix(const string prefix) { this.m_keyPrefix = prefix; }

    // 獲取鍵的字頭
    string GetKeyPrefix() const { return this.m_keyPrefix; }

    // 將鍵轉換為字符串
    string KeyToString(const string id) override { return id; }

    // 註冊新項目
    bool Register(const string name, const string description, Val value, string key = NULL) override
    {
        // 實現省略...
    }
};
```

## 2. 擴展 Registry 模組

### 2.1 創建新的註冊器類型

要創建新的註冊器類型，需要繼承 `Registry` 基類並實現抽象方法：

```cpp
// 創建使用枚舉作為鍵的註冊器
template <typename Val>
class EnumRegistry : public Registry<int, Val>
{
protected:
    string m_keyPrefix;                     // 鍵的字頭

public:
    // 構造函數
    EnumRegistry(const string name = "EnumRegistry", const int maxItems = 100)
        : Registry<int, Val>(name, maxItems)
    {
        m_keyPrefix = "ER"; // 預設字頭
    }

    // 生成唯一ID（對於枚舉，通常直接使用枚舉值作為鍵）
    int GenerateKey() override
    {
        // 實現省略...
    }

    // 設置鍵的字頭
    void SetKeyPrefix(const string prefix) { this.m_keyPrefix = prefix; }

    // 獲取鍵的字頭
    string GetKeyPrefix() const { return this.m_keyPrefix; }

    // 將鍵轉換為字符串
    string KeyToString(const int id) override
    {
        return this.m_keyPrefix + "_" + IntegerToString(id);
    }

    // 註冊新項目
    bool Register(const string name, const string description, Val value, int key = 0) override
    {
        // 實現省略...
    }
};
```

### 2.2 創建特殊用途的註冊器

可以創建特殊用途的註冊器，繼承現有註冊器類型並添加特定領域的功能：

```cpp
// 創建配置註冊器
class ConfigRegistry : public StringRegistry<string>
{
private:
    string m_configFile;                     // 配置文件路徑

public:
    // 構造函數
    ConfigRegistry(const string name = "ConfigRegistry", const int maxItems = 100)
        : StringRegistry<string>(name, maxItems)
    {
        this.SetKeyPrefix("CFG"); // 設置配置註冊器的字頭
    }

    // 設置配置文件路徑
    void SetConfigFile(const string path) { m_configFile = path; }

    // 獲取配置文件路徑
    string GetConfigFile() const { return m_configFile; }

    // 從配置文件加載項目
    bool LoadFromFile()
    {
        // 實現省略...
    }

    // 保存項目到配置文件
    bool SaveToFile()
    {
        // 實現省略...
    }
};
```

### 2.3 使用單例模式

可以為註冊器添加單例模式，確保在整個應用程序中只有一個實例：

```cpp
template <typename Val>
class SingletonRegistry : public StringRegistry<Val>
{
private:
    static SingletonRegistry<Val>* s_instance;  // 單例實例

    // 私有構造函數（單例模式）
    SingletonRegistry()
        : StringRegistry<Val>("SingletonRegistry", 100)
    {
        this.SetKeyPrefix("SR"); // 設置單例註冊器的字頭
    }

public:
    // 獲取單例實例
    static SingletonRegistry<Val>* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new SingletonRegistry<Val>();
        }
        return s_instance;
    }

    // 釋放單例實例
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }
};

// 初始化單例實例
template <typename Val>
SingletonRegistry<Val>* SingletonRegistry<Val>::s_instance = NULL;
```

## 3. 使用 Registry 模組的最佳實踐

### 3.1 選擇合適的註冊器類型

- 使用 `StringRegistry` 存儲字符串鍵值對
- 使用 `IntRegistry` 存儲整數鍵值對
- 使用 `PointerRegistry` 存儲對象指針
- 創建自定義註冊器處理特殊需求

### 3.2 管理註冊器生命週期

- 使用單例模式的註冊器在程序結束時釋放
- 非單例註冊器在不再需要時顯式刪除
- 在刪除註冊器前調用 `Clear()` 方法釋放所有項目

### 3.3 處理註冊項目

- 使用有意義的名稱和描述
- 檢查註冊和查詢操作的返回值
- 使用 `ForEach()` 方法遍歷項目
- 在更新項目值時，使用 `UpdateValue()` 方法而不是直接修改

### 3.4 優化性能

- 設置合理的最大項目數量
- 使用適當的鍵類型（字符串、整數、指針）
- 避免頻繁註冊和刪除操作
- 使用 `GetItem()` 方法獲取項目，避免重複查詢

## 4. 與其他模組集成

### 4.1 與 Pipeline 模組集成

```cpp
// 創建可註冊的流水線階段
template <typename Output, typename Input, typename Key, typename Val>
class RegistrablePipeline : public IPipeline<Output, Input>
{
protected:
    Registry<Key, Val>* m_registry;  // 註冊器指針

public:
    // 構造函數
    RegistrablePipeline(Registry<Key, Val>* registry)
        : m_registry(registry) {}

    // 註冊數據到註冊器
    bool Register(const string name, const string description, Val value, Key key = NULL)
    {
        if(m_registry == NULL) return false;
        return m_registry.Register(name, description, value, key);
    }

    // 獲取註冊器
    Registry<Key, Val>* GetRegistry() const { return m_registry; }
};
```

### 4.2 與 Stage 模組集成

```cpp
// 創建階段管理器
template <typename Output, typename Input, typename RegistryVal>
class StageManager : public PipelineManager<Output, Input>
{
protected:
    StageManagerRegistry<RegistryVal>* m_registry; // 註冊器成員
    string m_id;                      // 註冊器提供的ID
    string m_name;                    // 管理器名稱
    string m_description;             // 管理器描述

    // 構造函數
    StageManager(const string name, const string description)
        : PipelineManager<Output, Input>(name, true)
        , m_name(name)
        , m_description(description) {}

    // 初始化註冊器（由子類實現）
    virtual void InitRegistry() = 0;

    // 註冊自身（由子類實現）
    virtual void Register() = 0;
};
```

## 5. 故障排除

### 5.1 常見問題

- **註冊失敗**: 檢查是否達到最大項目數量或鍵是否已存在
- **查詢返回 NULL**: 檢查鍵是否正確或項目是否已被刪除
- **內存洩漏**: 確保在刪除註冊器前調用 `Clear()` 方法
- **鍵衝突**: 使用不同的前綴或自定義鍵生成邏輯

### 5.2 調試技巧

- 使用 `ToString()` 方法輸出項目信息
- 使用 `GetCount()` 方法檢查項目數量
- 使用 `ForEach()` 方法遍歷所有項目
- 檢查 `GetLastRegisteredKey()` 返回的最後註冊鍵

## 6. 性能優化

### 6.1 減少內存使用

- 設置合理的最大項目數量
- 在不再需要時刪除項目
- 使用整數或指針作為鍵，而不是字符串

### 6.2 提高查詢效率

- 使用哈希表實現快速查詢
- 避免在循環中重複查詢相同的項目
- 使用 `GetItem()` 方法獲取項目，然後直接訪問項目屬性

### 6.3 優化註冊和刪除操作

- 批量註冊項目
- 使用 `Clear()` 方法一次性刪除所有項目
- 避免頻繁註冊和刪除操作
