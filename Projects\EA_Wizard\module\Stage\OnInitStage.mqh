#property strict

#include "Enum.mqh"
#include "StageGroup.mqh"
#include "StageManager.mqh"
#include "../Util/Util.mqh"

// OnInit 階段群組已移至 StageGroup/OnInitStageGroup.mqh

// OnInit 開始階段
class OnInitStartStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static OnInitStartStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnInitStartStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    OnInitStartStage() : OnInitStageGroup("OnInitStartStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("OnInit開始階段註冊成功");
            } else {
                Print("警告: OnInit開始階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        Print("OnInit 開始執行");
        return INIT_SUCCEEDED;
    }
};

// 參數讀取階段
class ParameterReadStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static ParameterReadStage* instance = NULL;
        if(instance == NULL) {
            instance = new ParameterReadStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    ParameterReadStage() : OnInitStageGroup("ParameterReadStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("參數讀取階段註冊成功");
            } else {
                Print("警告: 參數讀取階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        // 在這裡實現參數讀取邏輯
        Print("讀取/設定參數");
        return INIT_SUCCEEDED;
    }
};

// 變數初始化階段
class VariableInitStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static VariableInitStage* instance = NULL;
        if(instance == NULL) {
            instance = new VariableInitStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    VariableInitStage() : OnInitStageGroup("VariableInitStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("變數初始化階段註冊成功");
            } else {
                Print("警告: 變數初始化階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        // 在這裡實現變數初始化邏輯
        Print("初始化變數");
        return INIT_SUCCEEDED;
    }
};

// 交易環境檢查階段
class TradingEnvironmentCheckStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static TradingEnvironmentCheckStage* instance = NULL;
        if(instance == NULL) {
            instance = new TradingEnvironmentCheckStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    TradingEnvironmentCheckStage() : OnInitStageGroup("TradingEnvironmentCheckStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("交易環境檢查階段註冊成功");
            } else {
                Print("警告: 交易環境檢查階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        // 檢查交易環境
        Print("檢查交易環境");
        return INIT_SUCCEEDED;
    }
};

// 指標初始化階段
class IndicatorInitStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static IndicatorInitStage* instance = NULL;
        if(instance == NULL) {
            instance = new IndicatorInitStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    IndicatorInitStage() : OnInitStageGroup("IndicatorInitStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("指標初始化階段註冊成功");
            } else {
                Print("警告: 指標初始化階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        // 初始化指標
        Print("載入/初始化指標");
        return INIT_SUCCEEDED;
    }
};

// OnInit 結束階段
class OnInitEndStage : public OnInitStageGroup {
private:
    static OnInitStageGroup* s_instance;  // 单例实例
    static OnInitStageGroup* GetInstanceInternal() {
        static OnInitEndStage* instance = NULL;
        if(instance == NULL) {
            instance = new OnInitEndStage();
        }
        return instance;
    }

    // 私有建構函數 - 用於工廠方法
    OnInitEndStage() : OnInitStageGroup("OnInitEndStage") {}

public:
    // 获取单例实例
    static OnInitStageGroup* GetInstance() {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();

            // 自动注册到管理器
            IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = OnInitStageManager::GetInstance();
            bool registered = manager.AddPipeline(s_instance);
            s_instance.SetRegistered(registered);

            if(registered) {
                Print("OnInit結束階段註冊成功");
            } else {
                Print("警告: OnInit結束階段註冊失敗");
            }
        }
        return s_instance;
    }

    // 執行階段
    ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        // 先執行父類的 Execute 方法
        ENUM_INIT_RETCODE result = OnInitStageGroup::Execute(in);
        if(result != INIT_SUCCEEDED) return result;

        // 完成初始化
        Print("初始化完成");
        return INIT_SUCCEEDED;
    }
};

// 初始化各階段的靜態成員
OnInitStageGroup* OnInitStartStage::s_instance = NULL;
OnInitStageGroup* ParameterReadStage::s_instance = NULL;
OnInitStageGroup* VariableInitStage::s_instance = NULL;
OnInitStageGroup* TradingEnvironmentCheckStage::s_instance = NULL;
OnInitStageGroup* IndicatorInitStage::s_instance = NULL;
OnInitStageGroup* OnInitEndStage::s_instance = NULL;

class OnInitStageMapper
{
private:
    static OnInitStageMapper* s_instance;
    Map<int, OnInitStageGroupFunc>* m_map;

    OnInitStageMapper()
    {
        m_map = new HashMap<int, OnInitStageGroupFunc>();
        InitializeMap();
    }

    ~OnInitStageMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)ONINIT_STAGE_START, OnInitStartStage::GetInstance);
        m_map.set((int)ONINIT_STAGE_PARAMETER_READ, ParameterReadStage::GetInstance);
        m_map.set((int)ONINIT_STAGE_VARIABLE_INIT, VariableInitStage::GetInstance);
        m_map.set((int)ONINIT_STAGE_TRADING_ENV_CHECK, TradingEnvironmentCheckStage::GetInstance);
        m_map.set((int)ONINIT_STAGE_INDICATOR_INIT, IndicatorInitStage::GetInstance);
        m_map.set((int)ONINIT_STAGE_END, OnInitEndStage::GetInstance);
    }

public:
    static OnInitStageMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new OnInitStageMapper();
        }
        return s_instance;
    }

    OnInitStageGroupFunc GetStageFunction(ENUM_ONINIT_STAGE stage)
    {
        return m_map.get((int)stage,NULL);
    }
};

OnInitStageMapper* OnInitStageMapper::s_instance = NULL;
