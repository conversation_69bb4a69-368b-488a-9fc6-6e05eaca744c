#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/Util/ErrorHandler.mqh"

// 初始化錯誤處理器階段
class InitErrorHandler : public OnInitStage
{
public:
    InitErrorHandler() : OnInitStage(ONINIT_STAGE_START){}
    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化錯誤處理器階段");

        ErrorHandler* error_handler = ErrorHandler::GetInstance();

        if(!this.IsRegistered()) {
            error_handler.HandleError("初始化錯誤處理器階段註冊失敗");
            return INIT_FAILED;
        }
        
        if(!Register("ErrorHandler", "錯誤處理器", error_handler, "ErrorHandler"))
        {
            error_handler.HandleError("錯誤處理器註冊失敗");
            return INIT_FAILED;
        }

        return INIT_SUCCEEDED;
    }
}init_error_handler_stage;
