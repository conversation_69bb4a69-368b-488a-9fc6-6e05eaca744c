//+------------------------------------------------------------------+
//|                                      PipelineStateSkipped.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "PipelineStateBase.mqh"
#include "../state/PipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已跳過狀態 - 實現已跳過狀態的行為                           |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStateSkipped : public PipelineStateBase<PipelineObj>
{
public:
    // 構造函數
    PipelineStateSkipped()
    : PipelineStateBase(PIPELINE_STATE_SKIPPED, "已跳過") {}
    
    // 析構函數
    ~PipelineStateSkipped() {}
    
    // 執行流水線
    void Execute(PipelineObj pipeline) override;
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline) override;
};

template <typename PipelineObj>
void PipelineStateSkipped::Execute(PipelineObj pipeline)
{
    // 已跳過狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已被跳過，不能執行");
}

template <typename PipelineObj>
void PipelineStateSkipped::Restore(PipelineObj pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new PipelineStatePending<PipelineObj>());
}
