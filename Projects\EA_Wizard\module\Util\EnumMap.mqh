//+------------------------------------------------------------------+
//| EnumMap.mqh                                                      |
//| Map implementation optimized for enum keys                       |
//+------------------------------------------------------------------+
#include "../mql4-lib-master/Collection/HashMap.mqh" // 包含 HashMap 類別定義
#include "Util.mqh"

//+------------------------------------------------------------------+
//| Equality comparer specialized for enum types                     |
//+------------------------------------------------------------------+
template<typename Enum>
class EnumEqualityComparer: public EqualityComparer<Enum>
  {
public:
   virtual bool       equals(const Enum left,const Enum right) const {return left==right;}
   virtual int        hash(const Enum value) const {return (int)value;}
  };

//+------------------------------------------------------------------+
//| Map implementation optimized for enum keys                       |
//+------------------------------------------------------------------+
template<typename Enum,typename Value>
class EnumMap: public HashMap<Enum,Value>
  {
public:
   // Constructor with default EnumEqualityComparer
   EnumMap(bool owned=false)
      :HashMap<Enum,Value>(new EnumEqualityComparer<Enum>(),owned){}
      
   // Constructor with custom comparer
   EnumMap(EqualityComparer<Enum>* comparer,bool owned=false)
      :HashMap<Enum,Value>(comparer,owned){}
  };
