//+------------------------------------------------------------------+
//|                                       PipelineStateFailed.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "PipelineStateBase.mqh"
#include "../state/PipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線執行失敗狀態 - 實現執行失敗狀態的行為                       |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStateFailed : public PipelineStateBase<PipelineObj>
{
public:
    // 構造函數
    PipelineStateFailed()
    : PipelineStateBase(PIPELINE_STATE_FAILED, "執行失敗") {}
    
    // 析構函數
    ~PipelineStateFailed() {}
    
    // 執行流水線
    void Execute(PipelineObj pipeline) override;
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline) override;
};

template <typename PipelineObj>
void PipelineStateFailed::Execute(PipelineObj pipeline)
{
    // 執行失敗狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 執行失敗，需要先重置才能再次執行");
}

template <typename PipelineObj>
void PipelineStateFailed::Restore(PipelineObj pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new PipelineStatePending<PipelineObj>());
}
