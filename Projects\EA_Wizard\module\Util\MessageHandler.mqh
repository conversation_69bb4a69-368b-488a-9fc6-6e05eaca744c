#property strict

#define MAX_QUEUE 100
#define MAX_TYPES 10

struct Message {
   int type;
   string content;
};

enum ENUM_MESSAGE_TYPE {
   MSG_TYPE_SYSTEM = 0,   // 系統訊息
   MSG_TYPE_TRADE = 1,    // 交易訊息
   MSG_TYPE_WARNING = 2,   // 警告訊息
   MSG_TYPE_ERROR = 3     // 錯誤訊息
};

typedef void (*MessageHandlerFunc)(Message &msg);

void HandleSystemMessage(Message &msg) {
   Print("系統訊息: ", msg.content);
}

void HandleTradeMessage(Message &msg) {
   Print("交易訊息: ", msg.content);
}

void HandleWarningMessage(Message &msg) {
   Print("警告訊息: ", msg.content);
}

class MessageHandler {
private:
   static MessageHandler* instance;
   Message m_messageQueue[];
   int m_queueSize;
   MessageHandlerFunc m_handlers[];
   int m_maxQueue;
   int m_maxTypes;

   // 私有建構子
   MessageHandler() {
      m_queueSize = 0;
      m_maxQueue = MAX_QUEUE;
      m_maxTypes = MAX_TYPES;
      ArrayResize(m_messageQueue, m_maxQueue);
      ArrayResize(m_handlers, m_maxTypes);
   }

public:
   // 獲取單例實例
   static MessageHandler* GetInstance() {
      if (instance == NULL) {
         instance = new MessageHandler();
      }
      return instance;
   }

   // 設置最大佇列大小
   void SetMaxQueue(int max) {
      if (max > 0) {
         m_maxQueue = max;
         ArrayResize(m_messageQueue, m_maxQueue);
      }
   }

   // 設置最大訊息類型數量
   void SetMaxTypes(int max) {
      if (max > 0) {
         m_maxTypes = max;
         ArrayResize(m_handlers, m_maxTypes);
      }
   }

   // 註冊 handler
   void RegisterHandler(ENUM_MESSAGE_TYPE msg_type, MessageHandlerFunc handler) {
      if(msg_type >= 0 && (int)msg_type < m_maxTypes)
         m_handlers[msg_type] = handler;
   }

   // 移除 handler
   void RemoveHandler(ENUM_MESSAGE_TYPE msg_type) {
      if(msg_type >= 0 && (int)msg_type < m_maxTypes)
         m_handlers[msg_type] = NULL;
   }

   // 加入訊息
   void AddMessage(ENUM_MESSAGE_TYPE msg_type, string content) {
      if(m_queueSize < m_maxQueue) {
         m_messageQueue[m_queueSize].type = (int)msg_type;
         m_messageQueue[m_queueSize].content = content;
         m_queueSize++;
      }
   }

   // 處理單一訊息
   void HandleMessage(Message &msg) {
      if(msg.type >= 0 && msg.type < m_maxTypes && m_handlers[msg.type] != NULL)
         m_handlers[msg.type](msg);
      else
         Print("No handler for message type: ", EnumToString(ENUM_MESSAGE_TYPE(msg.type)));
   }

   // 處理佇列
   void ProcessQueue() {
      for(int i = 0; i < m_queueSize; i++) {
         HandleMessage(m_messageQueue[i]);
      }
      ArrayFree(m_messageQueue);
      ArrayResize(m_messageQueue, m_maxQueue);
      m_queueSize = 0;
   }
};

// 初始化靜態實例指針
MessageHandler* MessageHandler::instance = NULL;

class MessageHandlerDriver
{
   private:
      MessageHandler* m_messageHandler;

   public:
      MessageHandlerDriver()
      {
         m_messageHandler = MessageHandler::GetInstance();
         Initialize();
      }

      void Initialize()
      {
         m_messageHandler.RegisterHandler(MSG_TYPE_SYSTEM, HandleSystemMessage);
         m_messageHandler.RegisterHandler(MSG_TYPE_TRADE, HandleTradeMessage);
         m_messageHandler.RegisterHandler(MSG_TYPE_WARNING, HandleWarningMessage);
      }

}init_message_handler_driver;