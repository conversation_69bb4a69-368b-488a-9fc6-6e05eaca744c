//+------------------------------------------------------------------+
//|                                              PipelineResult.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

//+------------------------------------------------------------------+
//| 流水線結果類 - 用於存儲流水線執行結果                            |
//+------------------------------------------------------------------+
class PipelineResult
{
private:
    bool m_success;      // 執行是否成功
    string m_message;    // 結果消息
    string m_source;     // 結果來源

public:
    // 構造函數
    PipelineResult(bool success, string message, string source)
    : m_success(success), m_message(message), m_source(source) {}

    // 析構函數
    ~PipelineResult() {}

    // 檢查是否成功
    bool IsSuccess() const { return m_success; }

    // 獲取消息
    string GetMessage() const { return m_message; }

    // 獲取來源
    string GetSource() const { return m_source; }
};