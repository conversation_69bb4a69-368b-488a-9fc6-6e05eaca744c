# Registry 模組 UML 圖表

## 類別圖 (Class Diagram)

```mermaid
classDiagram
    %% 基本介面和類別
    class RegistryItem~Val~ {
        -m_id: string
        -m_name: string
        -m_description: string
        -m_value: Val
        -m_createTime: datetime
        -m_type: string
        +RegistryItem(id, name, description, value, type)
        +GetId() string
        +GetName() string
        +GetDescription() string
        +GetValue() Val
        +GetCreateTime() datetime
        +ToString() string
        +GetType() string
    }

    %% 註冊器介面
    class IRegistry~Key,Val~ {
        <<interface>>
        +Register(name, description, value) RegistryResult~Key~*
        +Unregister(key) bool
        +Clear() void
        +GetName() string
        +GetCount() int
        +GetMaxItems() int
        +GetItem(key) RegistryItem~Val~*
        +GetAllKeys(Keys[]) int
        +GetLastRegisteredKey() Key
    }

    %% 註冊器基類
    class Registry~Key,Val~ {
        <<abstract>>
        #m_name: string
        #m_items: HashMap~Key,RegistryItem~Val~*~
        #m_maxItems: int
        #m_lastRegisteredKey: Key
        +Registry(name, type, maxItems)
        +~Registry()
        +Register(name, description, value) RegistryResult~Key~* virtual
        +Unregister(key) bool
        +Clear() void
        +GetName() string
        +GetCount() int
        +GetMaxItems() int
        +GetItem(key) RegistryItem~Val~*
        +GetAllKeys(Keys[]) int
        +GetLastRegisteredKey() Key
    }

    %% ItemRegistry 類
    class ItemRegistry~Val~ {
        +ItemRegistry(name, type, maxItems)
        +Register(name, description, value) RegistryResult~string~*
    }

    %% RegistryResult 類
    class RegistryResult~Key~ {
        +bool success
        +string message
        +Key key
        +string source
        +RegistryResult(success, message, key, source)
        +IsSuccess() bool
        +GetMessage() string
        +GetKey() Key
        +GetSource() string
        +ToString() string
    }

    %% 關係
    IRegistry <|.. Registry : implements
    IRegistry ..> RegistryItem : use
    Registry *-- HashMap : uses
    Registry <|-- ItemRegistry : inherits
    IRegistry ..> RegistryResult : use

```
