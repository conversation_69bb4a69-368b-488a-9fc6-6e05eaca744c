#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"
#include "SignalCheckStage.mqh"

// 風險控制檢查階段
class RiskCheckStage : public OnTickPipeline
{
private:
    string m_symbol;
    int m_magic_number;
    double m_max_risk_percent;
    double m_max_positions;
    SignalCheckStage* m_signal_stage;
    bool m_risk_allowed;
    
public:
    // 建構函數
    RiskCheckStage(SignalCheckStage* signal_stage, 
                  string symbol = NULL, 
                  int magic_number = 0,
                  double max_risk_percent = 2.0,
                  int max_positions = 5)
        : m_signal_stage(signal_stage),
          m_symbol(symbol == NULL ? Symbol() : symbol),
          m_magic_number(magic_number),
          m_max_risk_percent(max_risk_percent),
          m_max_positions(max_positions),
          m_risk_allowed(false)
    {
    }
    
    // 獲取風險是否允許
    bool IsRiskAllowed() const
    {
        return m_risk_allowed;
    }
    
    bool Execute(void* in = NULL) override
    {
        // 檢查信號階段是否有效
        if(m_signal_stage == NULL)
        {
            Print("信號檢查階段未設置");
            return false;
        }
        
        // 如果沒有信號，則不需要進行風險檢查
        if(!m_signal_stage.HasSignal())
        {
            Print("無交易信號，跳過風險檢查");
            m_risk_allowed = false;
            return true;
        }
        
        // 獲取信號類型
        int signal_type = m_signal_stage.GetSignalType();
        
        // 檢查帳戶餘額
        double account_balance = AccountBalance();
        if(account_balance <= 0)
        {
            Print("帳戶餘額不足");
            m_risk_allowed = false;
            return true;
        }
        
        // 檢查帳戶權益
        double account_equity = AccountEquity();
        double equity_percent = account_equity / account_balance * 100.0;
        if(equity_percent < 80.0) // 如果權益低於餘額的80%，則不允許交易
        {
            Print("帳戶權益低於餘額的80%: ", DoubleToString(equity_percent, 2), "%");
            m_risk_allowed = false;
            return true;
        }
        
        // 檢查保證金水平
        double margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
        if(margin_level < 200.0) // 如果保證金水平低於200%，則不允許交易
        {
            Print("保證金水平過低: ", DoubleToString(margin_level, 2), "%");
            m_risk_allowed = false;
            return true;
        }
        
        // 檢查持倉數量
        int position_count = 0;
        for(int i = 0; i < OrdersTotal(); i++)
        {
            if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
            {
                if((m_magic_number == 0 || OrderMagicNumber() == m_magic_number) && 
                   (m_symbol == "" || OrderSymbol() == m_symbol))
                {
                    position_count++;
                }
            }
        }
        
        if(position_count >= m_max_positions)
        {
            Print("已達到最大持倉數量: ", position_count, " >= ", m_max_positions);
            m_risk_allowed = false;
            return true;
        }
        
        // 檢查風險百分比
        double risk_amount = 0.0;
        double lot_size = 0.01; // 假設交易量為0.01手
        double stop_loss_pips = 20.0; // 假設止損為20點
        double point_value = MarketInfo(m_symbol, MODE_TICKVALUE) * 10; // 每點價值
        
        risk_amount = lot_size * stop_loss_pips * point_value;
        double risk_percent = risk_amount / account_balance * 100.0;
        
        if(risk_percent > m_max_risk_percent)
        {
            Print("風險百分比過高: ", DoubleToString(risk_percent, 2), "% > ", DoubleToString(m_max_risk_percent, 2), "%");
            m_risk_allowed = false;
            return true;
        }
        
        // 風險檢查通過
        m_risk_allowed = true;
        Print("風險檢查通過 - 交易品種: ", m_symbol, 
              ", 信號類型: ", (signal_type == SIGNAL_BUY ? "買入" : "賣出"),
              ", 風險百分比: ", DoubleToString(risk_percent, 2), "%",
              ", 持倉數量: ", position_count);
        
        return true;
    }
};
