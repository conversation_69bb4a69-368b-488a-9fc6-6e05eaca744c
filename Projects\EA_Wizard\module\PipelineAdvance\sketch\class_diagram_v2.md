# PipelineAdvance 模組類別圖 (v2)

PipelineAdvance 模組實現了組合模式的流水線處理架構，允許將複雜的處理邏輯分解為一系列獨立的階段，每個階段專注於特定的任務，從而提高代碼的可維護性和可擴展性。

## 核心概念

- **IPipeline**: 流水線介面，定義了基本的流水線操作
- **Pipeline**: 抽象基類，實現了 IPipeline 介面的基本功能
- **PipelineComposite**: 組合類，實現了組合模式，可以包含多個子流水線
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 類別圖

```mermaid
classDiagram
    %% 核心介面和類別
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        +PipelineResult(bool success, string message, string source)
        +bool IsSuccess()
        +string GetMessage()
        +string GetSource()
    }

    class IPipeline {
        <<interface>>
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    class Pipeline {
        <<abstract>>
        -string m_name
        -string m_type
        -PipelineResult* m_result
        -bool m_executed
        +Pipeline(string name, string type)
        +~Pipeline()
        +virtual void Execute() = 0
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        #void SetResult(bool success, string message, string source)
    }

    class PipelineComposite {
        -string m_name
        -string m_type
        -Vector~IPipeline*~ m_children
        -PipelineResult* m_result
        -bool m_executed
        -int m_max_children
        +PipelineComposite(string name, string type, int maxItems)
        +~PipelineComposite()
        +PipelineResult* Add(IPipeline* child)
        +PipelineResult* Remove(IPipeline* child)
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        +int GetMaxItems()
        +int GetCount()
        -void SetResult(bool success, string message, string source)
    }

    %% 具體流水線類型
    class DataFeedPipeline {
        +DataFeedPipeline(string name)
    }
    note for DataFeedPipeline "PIPELINE_DATAFEED = 'DataFeedPipeline'"

    class SignalPipeline {
        +SignalPipeline(string name)
    }
    note for SignalPipeline "PIPELINE_SIGNAL = 'SignalPipeline'"

    class OrderPipeline {
        +OrderPipeline(string name)
    }
    note for OrderPipeline "PIPELINE_ORDER = 'OrderPipeline'"

    class RiskPipeline {
        +RiskPipeline(string name)
    }
    note for RiskPipeline "PIPELINE_RISK = 'RiskPipeline'"

    class LogPipeline {
        +LogPipeline(string name)
    }
    note for LogPipeline "PIPELINE_LOG = 'LogPipeline'"

    class ErrorPipeline {
        +ErrorPipeline(string name)
    }
    note for ErrorPipeline "PIPELINE_ERROR = 'ErrorPipeline'"

    %% 關係
    IPipeline --> PipelineResult : uses
    IPipeline <|.. Pipeline : implements
    IPipeline <|.. PipelineComposite : implements
    PipelineComposite <|-- DataFeedPipeline : extends
    PipelineComposite <|-- SignalPipeline : extends
    PipelineComposite <|-- OrderPipeline : extends
    PipelineComposite <|-- RiskPipeline : extends
    PipelineComposite <|-- LogPipeline : extends
    PipelineComposite <|-- ErrorPipeline : extends
    PipelineComposite o-- IPipeline : contains
```

## 流水線執行流程

```mermaid
flowchart TD
    A[開始執行流水線] --> B{檢查是否已執行}
    B -->|已執行| C[直接返回]
    B -->|未執行| D{檢查是否有子流水線}
    D -->|沒有子流水線| E[設置成功結果並返回]
    D -->|有子流水線| F[檢查所有子流水線是否都未執行]
    F -->|有已執行的子流水線| G[設置失敗結果並返回]
    F -->|所有子流水線都未執行| H[執行第一個子流水線]
    H --> I{檢查子流水線結果}
    I -->|失敗| J[設置失敗結果並返回]
    I -->|成功| K{是否還有子流水線}
    K -->|是| L[執行下一個子流水線]
    L --> I
    K -->|否| M[設置成功結果並返回]
    C --> N[結束]
    E --> N
    G --> N
    J --> N
    M --> N
```

## 使用示例

```mq4
// 創建數據饋送流水線
DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據");

// 創建信號流水線
SignalPipeline* signal = new SignalPipeline("交易信號");

// 創建訂單流水線
OrderPipeline* order = new OrderPipeline("訂單處理");

// 創建風險控制流水線
RiskPipeline* risk = new RiskPipeline("風險控制");

// 創建日誌流水線
LogPipeline* log = new LogPipeline("日誌記錄");

// 創建錯誤處理流水線
ErrorPipeline* error = new ErrorPipeline("錯誤處理");

// 創建主流水線
PipelineComposite* mainPipeline = new PipelineComposite("主流水線", "Main");

// 添加子流水線
mainPipeline.Add(dataFeed);
mainPipeline.Add(signal);
mainPipeline.Add(order);
mainPipeline.Add(risk);
mainPipeline.Add(log);
mainPipeline.Add(error);

// 檢查子流水線數量
Print("子流水線數量: ", mainPipeline.GetCount());
Print("最大子流水線數量: ", mainPipeline.GetMaxItems());

// 執行流水線
mainPipeline.Execute();

// 獲取結果
PipelineResult* result = mainPipeline.GetResult();
if(result.IsSuccess())
{
    Print("流水線執行成功: ", result.GetMessage());
}
else
{
    Print("流水線執行失敗: ", result.GetMessage(), ", 來源: ", result.GetSource());
}

// 清理資源
delete mainPipeline; // 這將自動清理所有子流水線
```
