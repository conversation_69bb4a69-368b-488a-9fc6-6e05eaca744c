#property strict

#include "Pipeline.mqh"

//+------------------------------------------------------------------+
//| 流水線管理器介面 - 定義流水線管理器的基本功能                    |
//+------------------------------------------------------------------+
template <typename Output, typename Input>
interface IPipelineManager : public IPipelineGroup<Output, Input>
{
public:
    string GetName() const;
    void SetName(const string name);
    bool GetStopOnError() const;
    void SetStopOnError(bool stopOnError);

    bool IsError(Output result);

};

//+------------------------------------------------------------------+
//| 流水線管理器 - 用於管理和執行流水線階段                           |
//+------------------------------------------------------------------+
template <typename Output, typename Input>
class PipelineManager : public IPipelineGroup<Output, Input>
{
private:
    PipelineGroupBase<Output, Input> m_pipeline_group; // 流水線群組

    bool m_is_error;
    string m_last_error;

    string m_name;                   // 管理器名稱
    bool m_stopOnError;              // 是否在錯誤時停止執行

public:
    // 建構函數
    PipelineManager(const string name = "DefaultPipelineManager", bool stopOnError = true)
        : m_pipeline_group(), m_name(name), m_stopOnError(stopOnError) {}

    PipelineManager(IPipeline<Output, Input>* &pipelines[], const string name = "DefaultPipelineManager", bool stopOnError = true)
        : m_pipeline_group(pipelines), m_name(name), m_stopOnError(stopOnError) {}

    // 解構函數
    ~PipelineManager() {}

    // 是否有流水線階段
    bool HasPipeline(IPipeline<Output, Input>* pipeline) const override
    {
        return m_pipeline_group.HasPipeline(pipeline);
    }

    // 添加流水線階段
    bool AddPipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_pipeline_group.AddPipeline(pipeline);
    }

    // 移除流水線階段
    bool RemovePipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_pipeline_group.RemovePipeline(pipeline);
    }

    // 清除所有流水線階段
    void ClearPipelines() override
    {
        m_pipeline_group.ClearPipelines();
    }

    // 獲取所有流水線階段
    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const override
    {
        return m_pipeline_group.GetPipelines(pipelines);
    }

    bool IsError() override
    {
        return m_is_error;
    }

    string LastError() override
    {
        return m_last_error;
    }

    // 獲取管理器名稱
    string GetName() const { return m_name; }

    // 設置管理器名稱
    void SetName(const string name) { m_name = name; }

    // 獲取是否在錯誤時停止執行
    bool GetStopOnError() const { return m_stopOnError; }

    // 設置是否在錯誤時停止執行
    void SetStopOnError(bool stopOnError) { m_stopOnError = stopOnError; }

    // 執行流水線
    virtual Output Execute(Input in = NULL) override
    {
        IPipeline<Output, Input>* pipelines[];
        int count = m_pipeline_group.GetPipelines(pipelines);

        Print("開始執行流水線管理器: ", m_name, ", 階段數量: ", count);

        // 如果沒有階段，返回預設值
        if(count == 0)
        {
            Print("警告: 流水線管理器沒有階段");
            return NULL;
        }

        // 執行流水線階段
        for(int i = 0; i < count; i++)
        {
            Print("執行階段 ", i+1, "/", count);
            Output result = pipelines[i].Execute(in);

            // 如果設置了停止於錯誤，並且結果為錯誤，則停止執行
            bool isErrorResult = IsError(result);
            if(m_stopOnError && isErrorResult)
            {
                m_is_error = true;
                m_last_error = "階段 " + IntegerToString(i+1) + " 執行失敗，停止流水線";
                Print("流水線管理器執行失敗: ", m_name, ", 階段: ", i+1, ", 錯誤: ", pipelines[i].LastError());
                return result;
            }
        }

        Print("流水線管理器執行完成: ", m_name);
        return pipelines[count-1].Execute(in); // 返回最後一個階段的結果
    }

 protected:
    // 判斷結果是否為錯誤（由子類實現）
    virtual bool IsError(Output result) { return false; }
};
