//+------------------------------------------------------------------+
//|                                                 IntRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Registry.mqh"
#include "../mql4-lib-master/Lang/Pointer.mqh"
#include "../mql4-lib-master/Lang/Hash.mqh"

//+------------------------------------------------------------------+
//| 整數註冊器類 - 用於註冊和管理整數類型的對象                        |
//+------------------------------------------------------------------+
template <typename Val>
class IntRegistry : public Registry<int, Val>
{
protected:
    int m_keyPrefix;                     // 鍵的字頭基數

public:
    // 公開構造函數（移除單例模式）
    IntRegistry(const string name = "IntRegistry", const int maxItems = 100)
        : Registry<int, Val>(name, maxItems)
    {
        m_keyPrefix = 1000; // 預設字頭基數
    }

    ~IntRegistry() {
    }

    // 生成唯一ID
    int GenerateKey() override
    {
        // 使用 GetAddress 和 MurmurHash3_x86_32 生成唯一的鍵
        long address = GetAddress(&this);
        string addressStr = IntegerToString(address);
        uint seed = (uint)TimeCurrent(); // 使用當前時間作為種子
        uint hash = MurmurHash3_x86_32(addressStr, seed);

        // 返回字頭加哈希值的一部分，確保它是一個有效的整數
        return this.m_keyPrefix + (int)(hash % 1000000);
    }

    // 設置鍵的字頭基數
    void SetKeyPrefix(const int prefix)
    {
        this.m_keyPrefix = prefix;
    }

    // 獲取鍵的字頭基數
    int GetKeyPrefix() const
    {
        return this.m_keyPrefix;
    }

    // 將鍵轉換為字符串
    string KeyToString(const int id) override
    {
        return IntegerToString(id); // 整數鍵轉換為字符串
    }

    // 註冊新項目
    bool Register(const string name, const string description, Val value, int key = 0) override
    {
        // 檢查是否達到最大項目數量
        if(this.m_items.size() >= this.m_maxItems)
        {
            Print("註冊失敗：已達到最大項目數量 ", this.m_maxItems);
            return false;
        }

        // 生成唯一ID或使用提供的鍵
        int id;
        if(key == 0)
        {
            id = GenerateKey();
        }
        else
        {
            id = key;
            // 檢查鍵是否已存在
            if(this.m_items.contains(id))
            {
                Print("註冊失敗：鍵已存在");
                return false;
            }
        }

        // 創建新項目
        string idStr = IntegerToString(id);
        IRegistryItem<Val>* item = new RegistryItem<Val>(idStr, name, description, value);

        // 添加到哈希表
        this.m_items.set(id, item);

        // 保存最後註冊的鍵
        this.m_lastRegisteredKey = id;

        Print("註冊成功：", item.ToString());
        return true;
    }
};
