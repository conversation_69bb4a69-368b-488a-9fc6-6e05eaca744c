#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/mql4-lib-master/Trade/OrderTracker.mqh"
#include "../../module/Util/ErrorHandler.mqh"
#include "../../module/Trade/SignalHandler.mqh"

// 初始化訂單追蹤器階段
class SignalCheck : public OnTickStage
{
public:
    // 建構函數
    SignalCheck() : OnTickStage(ONTICK_STAGE_SIGNAL_CHECK) {}
    bool Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("訂單檢查階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            ExpertRemove();
        }

        // 獲取訂單追蹤器實例
        OrderTracker* order_tracker = dynamic_cast<OrderTracker*>(GetRegistry().GetValue("OrderTracker", NULL));
        if(order_tracker == NULL) {
            error_handler.HandleError("訂單追蹤器未初始化");
            ExpertRemove();
        }

        // 獲取單一交易信號處理器實例
        SignalHandler* signal_handler = dynamic_cast<SignalHandler*>(GetRegistry().GetValue("SignalHandler", NULL));
        if(signal_handler == NULL) {
            error_handler.HandleError("單一交易信號處理器未初始化");
            ExpertRemove();
        }

        // 獲取所有訂單
        Vector<Order*>* orders = new Vector<Order*>();
        order_tracker.getOrders(orders);

        // 清除之前的信號
        signal_handler.SetSignal(SIGNAL_NONE);

        // 如果沒有訂單，則生成新的買入信號
        if(orders.isEmpty()){
            signal_handler.SetSignal(SIGNAL_BUY, Close[0], 1.0, "SignalCheck");
            Print("生成新的買入信號");
        }

        // 否則如果訂單巳經存在交易單庫超過10分鐘，則平倉信號
        else if(!orders.isEmpty()){
            Order* order = orders.get(0);
            if(order.getOpenTime() < TimeCurrent() - 600){
                signal_handler.SetSignal(SIGNAL_CLOSE, Close[0], 1.0, "SignalCheck");
                Print("生成新的平倉信號");
            }
        }
        return true;
    }
}signal_check_stage;
