#property strict

#include "ValidationConfig.mqh"

//+------------------------------------------------------------------+
//| 驗證結果類                                                          |
//| 用於存儲和管理驗證的結果                                             |
//+------------------------------------------------------------------+
class CValidationResult
{
private:
    bool   m_isValid;      // 驗證結果
    string m_message;      // 錯誤訊息
    string m_identifier;   // 驗證對象標識符
    string m_source;       // 驗證來源
    int    m_errorCode;    // 錯誤代碼
    int    m_messageLevel; // 訊息級別

public:
    // 建構函數
    CValidationResult()
        : m_isValid(VALIDATOR_RESULT_VALID)
        , m_message("")
        , m_identifier("")
        , m_source(VALIDATOR_SOURCE_DEFAULT)
        , m_errorCode(VALIDATOR_ERROR_NONE)
        , m_messageLevel(VALIDATOR_MSG_INFO)
    {}
    
    // Getters
    bool   IsValid()       const { return m_isValid; }
    string GetMessage()    const { return m_message; }
    string GetIdentifier() const { return m_identifier; }
    string GetSource()     const { return m_source; }
    int    GetErrorCode()  const { return m_errorCode; }
    int    GetMessageLevel() const { return m_messageLevel; }
    
    // 設置驗證失敗
    void SetInvalid(const string identifier, const string msg, 
                    const string source = VALIDATOR_SOURCE_DEFAULT,
                    const int errorCode = VALIDATOR_ERROR_CUSTOM,
                    const int msgLevel = VALIDATOR_MSG_ERROR) 
    {
        m_isValid = VALIDATOR_RESULT_INVALID;
        m_identifier = identifier;
        m_message = msg;
        m_source = source;
        m_errorCode = errorCode;
        m_messageLevel = msgLevel;
    }
    
    // 合併另一個驗證結果
    void Merge(const CValidationResult* other) 
    {
        if(other != NULL && !other.IsValid()) 
        {
            SetInvalid(
                other.GetIdentifier(), 
                other.GetMessage(), 
                other.GetSource(),
                other.GetErrorCode(),
                other.GetMessageLevel()
            );
        }
    }

    // 清除驗證結果
    void Clear() 
    {
        m_isValid = VALIDATOR_RESULT_VALID;
        m_message = "";
        m_identifier = "";
        m_source = VALIDATOR_SOURCE_DEFAULT;
        m_errorCode = VALIDATOR_ERROR_NONE;
        m_messageLevel = VALIDATOR_MSG_INFO;
    }
    
    // 轉換為字符串（用於調試）
    string ToString() const
    {
        string status = m_isValid ? "有效" : "無效";
        string levelStr = "";
        
        switch(m_messageLevel)
        {
            case VALIDATOR_MSG_INFO:     levelStr = "信息"; break;
            case VALIDATOR_MSG_WARNING:  levelStr = "警告"; break;
            case VALIDATOR_MSG_ERROR:    levelStr = "錯誤"; break;
            case VALIDATOR_MSG_CRITICAL: levelStr = "嚴重錯誤"; break;
            default:                     levelStr = "未知"; break;
        }
        
        return StringFormat(
            "驗證結果: %s, 標識符: %s, 訊息: %s, 來源: %s, 錯誤代碼: %d, 級別: %s",
            status, m_identifier, m_message, m_source, m_errorCode, levelStr
        );
    }
};
