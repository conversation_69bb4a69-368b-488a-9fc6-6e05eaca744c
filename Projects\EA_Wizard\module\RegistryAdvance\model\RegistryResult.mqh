//+------------------------------------------------------------------+
//|                                              RegistryResult.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

//+------------------------------------------------------------------+
//| 註冊結果類 - 用於存儲註冊操作的結果                              |
//+------------------------------------------------------------------+
template<typename Key>
class RegistryResult
{
private:
    bool m_success;      // 操作是否成功
    string m_message;    // 結果消息
    Key m_key;           // 註冊的鍵（如果適用）
    string m_source;     // 結果來源

public:
    // 構造函數
    RegistryResult(bool success, string message, Key key, string source = "")
        : m_success(success), m_message(message), m_key(key), m_source(source) {}

    // 析構函數
    ~RegistryResult() {}

    // 檢查是否成功
    bool IsSuccess() const { return m_success; }

    // 獲取消息
    string GetMessage() const { return m_message; }

    // 獲取鍵
    Key GetKey() const { return m_key; }

    // 獲取來源
    string GetSource() const { return m_source; }

    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("Success: %s, Message: %s, Key: %s, Source: %s",
                            m_success ? "true" : "false", m_message, m_key, m_source);
    }
};
