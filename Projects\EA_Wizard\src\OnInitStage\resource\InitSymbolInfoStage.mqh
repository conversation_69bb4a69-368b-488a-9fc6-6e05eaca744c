#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"

// 初始化交易品種信息階段
class InitSymbolInfoStage : public OnInitPipeline
{
private:
    string m_symbol;
    int m_digits;
    double m_point;
    double m_tick_size;
    double m_tick_value;
    double m_min_lot;
    double m_max_lot;
    double m_lot_step;
    double m_swap_long;
    double m_swap_short;
    
public:
    // 建構函數
    InitSymbolInfoStage(string symbol = NULL)
        : m_symbol(symbol == NULL ? Symbol() : symbol)
    {
    }
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 獲取交易品種信息
        m_digits = (int)MarketInfo(m_symbol, MODE_DIGITS);
        m_point = MarketInfo(m_symbol, MODE_POINT);
        m_tick_size = MarketInfo(m_symbol, MODE_TICKSIZE);
        m_tick_value = MarketInfo(m_symbol, MODE_TICKVALUE);
        m_min_lot = MarketInfo(m_symbol, MODE_MINLOT);
        m_max_lot = MarketInfo(m_symbol, MODE_MAXLOT);
        m_lot_step = MarketInfo(m_symbol, MODE_LOTSTEP);
        m_swap_long = MarketInfo(m_symbol, MODE_SWAPLONG);
        m_swap_short = MarketInfo(m_symbol, MODE_SWAPSHORT);
        
        // 檢查交易品種信息是否有效
        if(m_digits <= 0 || m_point <= 0 || m_tick_size <= 0 || m_tick_value <= 0 || 
           m_min_lot <= 0 || m_max_lot <= 0 || m_lot_step <= 0)
        {
            Print("交易品種信息無效: ", m_symbol);
            return INIT_FAILED;
        }
        
        // 將交易品種信息註冊到註冊器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        
        // 註冊交易品種信息
        registry.Register("Symbol", "交易品種", m_symbol);
        registry.Register("Digits", "小數位數", &m_digits);
        registry.Register("Point", "點值", &m_point);
        registry.Register("TickSize", "最小價格變動", &m_tick_size);
        registry.Register("TickValue", "最小價格變動的價值", &m_tick_value);
        registry.Register("MinLot", "最小交易量", &m_min_lot);
        registry.Register("MaxLot", "最大交易量", &m_max_lot);
        registry.Register("LotStep", "交易量步長", &m_lot_step);
        registry.Register("SwapLong", "多頭隔夜利息", &m_swap_long);
        registry.Register("SwapShort", "空頭隔夜利息", &m_swap_short);
        
        Print("交易品種信息初始化成功: ", m_symbol);
        return INIT_SUCCEEDED;
    }
};
