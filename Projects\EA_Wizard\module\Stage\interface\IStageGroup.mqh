#property strict

#include "../../Pipeline/Pipeline.mqh"

//+------------------------------------------------------------------+
//| 階段群組介面 - 定義階段群組的基本功能                            |
//+------------------------------------------------------------------+
template <typename Output, typename Input>
interface IStageGroup : public IPipelineGroup<Output, Input>
{
public:
    string GetName() const;

    bool IsRegistered() const;
    void SetRegistered(bool registered);
};
