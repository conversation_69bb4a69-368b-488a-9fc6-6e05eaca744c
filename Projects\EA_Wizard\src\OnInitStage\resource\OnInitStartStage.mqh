#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"

// OnInit 開始階段
class OnInitStartStage : public OnInitPipeline
{
public:
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 檢查是否連接到交易伺服器
        if(!IsConnected())
        {
            Print("未連接到交易伺服器");
            return INIT_FAILED;
        }
        
        // 檢查交易是否允許
        if(!IsTradeAllowed())
        {
            Print("交易不允許");
            return INIT_FAILED;
        }
        
        Print("OnInit 開始執行");
        return INIT_SUCCEEDED;
    }
};
