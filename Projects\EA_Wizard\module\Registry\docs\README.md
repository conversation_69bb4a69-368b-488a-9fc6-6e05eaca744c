# Registry 模組文檔

## 文檔目錄

1. [UML 圖表](Registry_UML.md) - 包含類別圖、流程圖和時序圖
2. [架構文檔](Registry_Architecture.md) - 詳細介紹 Registry 模組的架構和設計
3. [實現指南](Registry_Implementation_Guide.md) - 提供 Registry 模組的實現詳解和擴展指南

## 概述

Registry 模組是 EA_Wizard 專案中的核心組件，提供了一個通用的註冊和管理各種類型對象的機制。它採用泛型設計，支持不同類型的鍵和值，並提供了豐富的功能，包括註冊、查詢、更新和刪除項目。

## 主要特點

- 支持註冊、查詢、更新和移除項目
- 使用泛型設計，可以註冊不同類型的對象
- 支持使用不同類型的鍵（string、int、enum 等）
- 實現單例模式，方便全局訪問
- 基於 mql4-lib-master 的 HashMap 實現高效存儲
- 支持項目元數據（ID、名稱、描述、創建時間、更新時間）
- 提供遍歷和回調機制

## 核心組件

- **Registry**: 所有註冊器的抽象基類
- **RegistryItem**: 註冊項目的基本結構
- **StringRegistry**: 使用字符串作為鍵的註冊器
- **IntRegistry**: 使用整數作為鍵的註冊器
- **PointerRegistry**: 使用指針作為鍵的註冊器
- **StageRegistry**: 用於註冊和管理階段對象的特殊註冊器
- **StageManagerRegistry**: 用於註冊和管理階段管理器的特殊註冊器

## 使用示例

```cpp
// 獲取字符串註冊器實例
Registry<string, string>* stringRegistry = Registry<string, string>::GetInstance("StringRegistry");

// 註冊項目
string id = stringRegistry.Register("Symbol", "交易品種", "EURUSD");

// 獲取項目值
string symbol = stringRegistry.GetValue(id);

// 更新項目值
stringRegistry.UpdateValue(id, "GBPUSD");

// 移除項目
stringRegistry.Unregister(id);
```

## 與其他模組的關係

Registry 模組與 Pipeline 和 Stage 模組緊密集成，用於存儲和管理流水線階段和階段管理器。它是 EA_Wizard 專案中的核心基礎設施，為其他模組提供了統一的對象註冊和管理機制。
