# PipelineAdvance 模組文檔

本目錄包含 PipelineAdvance 模組的設計文檔和圖表，用於說明模組的架構、類別關係和執行流程。

## 類別圖

- [類別圖 v1](./class_diagram_v1.md)：PipelineAdvance 模組的初始類別結構和關係
- [類別圖 v2](./class_diagram_v2.md)：更新後的類別圖，包含最新的方法和屬性（如 GetMaxItems 和 GetCount）

## 序列圖

- [序列圖 v1](./sequence_diagram_v1.md)：展示 PipelineAdvance 模組的執行序列，包括基本流程、錯誤處理和容量限制

## 其他圖表

- [原始類別圖](./class_digram.md)：最初的類別圖設計

## 核心概念

PipelineAdvance 模組實現了組合模式的流水線處理架構，允許將複雜的處理邏輯分解為一系列獨立的階段，每個階段專注於特定的任務，從而提高代碼的可維護性和可擴展性。

### 主要組件

1. **IPipeline**：流水線介面，定義了基本的流水線操作
2. **Pipeline**：抽象基類，實現了 IPipeline 介面的基本功能
3. **PipelineComposite**：組合類，實現了組合模式，可以包含多個子流水線
4. **PipelineResult**：結果類，用於存儲流水線執行結果

### 特點

- **組合模式**：允許將多個流水線組合成一個更大的流水線
- **容量限制**：可以限制子流水線的最大數量
- **執行狀態檢查**：防止重複執行已執行的流水線
- **錯誤處理**：提供詳細的錯誤信息和來源
- **資源管理**：自動清理子流水線資源

## 使用方法

請參考各文檔中的使用示例，了解如何創建、配置和執行流水線。
