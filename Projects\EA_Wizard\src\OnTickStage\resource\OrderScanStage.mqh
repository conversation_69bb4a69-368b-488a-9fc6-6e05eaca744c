#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// 訂單掃描階段
class OrderScanStage : public OnTickPipeline
{
private:
    int m_magic_number;
    string m_symbol;
    
public:
    // 建構函數
    OrderScanStage(int magic_number = 0, string symbol = NULL)
        : m_magic_number(magic_number),
          m_symbol(symbol == NULL ? Symbol() : symbol)
    {
    }
    
    bool Execute(void* in = NULL) override
    {
        // 掃描當前訂單
        int total_orders = OrdersTotal();
        int open_buy_orders = 0;
        int open_sell_orders = 0;
        double total_buy_volume = 0.0;
        double total_sell_volume = 0.0;
        
        for(int i = 0; i < total_orders; i++)
        {
            if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
                continue;
                
            // 如果指定了魔術數字，則只處理該魔術數字的訂單
            if(m_magic_number > 0 && OrderMagicNumber() != m_magic_number)
                continue;
                
            // 如果指定了交易品種，則只處理該交易品種的訂單
            if(m_symbol != "" && OrderSymbol() != m_symbol)
                continue;
                
            // 統計買入和賣出訂單
            if(OrderType() == OP_BUY)
            {
                open_buy_orders++;
                total_buy_volume += OrderLots();
            }
            else if(OrderType() == OP_SELL)
            {
                open_sell_orders++;
                total_sell_volume += OrderLots();
            }
        }
        
        // 輸出訂單掃描結果
        Print("訂單掃描結果 - 交易品種: ", m_symbol, 
              ", 魔術數字: ", m_magic_number,
              ", 買入訂單: ", open_buy_orders, 
              ", 賣出訂單: ", open_sell_orders,
              ", 買入總量: ", DoubleToString(total_buy_volume, 2),
              ", 賣出總量: ", DoubleToString(total_sell_volume, 2));
        
        // 可以在這裡添加更多的訂單處理邏輯
        // 例如: 檢查是否有需要平倉的訂單、計算訂單的盈虧情況等
        
        return true;
    }
};
