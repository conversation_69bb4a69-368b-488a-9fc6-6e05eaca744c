#property strict

#include "interface/IStageGroup.mqh"
#include "Enum.mqh"
#include "StageManager.mqh"
#include "Decorator/StageGroupDecorator.mqh"

//+------------------------------------------------------------------+
//| 階段群組基類 - 用於實現各種 EA 階段群組                           |
//+------------------------------------------------------------------+
template <typename Output, typename Input>
class StageGroupEntity : public IStageGroup<Output, Input> {
private:
    PipelineGroupBase<Output, Input> m_pipeline_group; // 流水線群組

    string m_name;

    bool m_is_error;
    string m_last_error;

protected:
    bool m_registered;                                    // 是否已註冊到管理器

public:
    // 受保護的建構函數 - 允許子類調用
    StageGroupEntity(string name="StageGroupEntity")
    : m_pipeline_group() {
        m_name = name;
        m_is_error = false;
        m_last_error = "No Error";
        m_registered = false;
    }

    // 受保護的建構函數 - 允許子類調用，帶流水線陣列
    StageGroupEntity(IPipeline<Output, Input>* &pipelines[], string name="StageGroupEntity")
    : m_pipeline_group(pipelines) {
        m_name = name;
        m_is_error = false;
        m_last_error = "No Error";
        m_registered = false;
    }
    // 解構函數
    ~StageGroupEntity() {}

    // 是否有流水線階段
    bool HasPipeline(IPipeline<Output, Input>* pipeline) const override
    {
        return m_pipeline_group.HasPipeline(pipeline);
    }

    // 添加流水線階段
    bool AddPipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_pipeline_group.AddPipeline(pipeline);
    }

    // 移除流水線階段
    bool RemovePipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_pipeline_group.RemovePipeline(pipeline);
    }

    // 清除所有流水線階段
    void ClearPipelines() override
    {
        m_pipeline_group.ClearPipelines();
    }

    // 獲取所有流水線階段
    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const override
    {
        return m_pipeline_group.GetPipelines(pipelines);
    }

    bool IsError() override
    {
        return m_is_error;
    }

    string LastError() override
    {
        return m_last_error;
    }

    // 獲取是否已註冊到管理器
    bool IsRegistered() const { return m_registered; }

    // 設置註冊狀態
    void SetRegistered(bool registered) { m_registered = registered; }

    // 執行階段群組（由子類實現具體邏輯）
    virtual Output Execute(Input in) { return NULL; }

    virtual string GetName() const override { return m_name; }

    // 靜態工廠方法 - 創建未註冊的實例（由子類實現）
    // static StageGroupEntity<Output, Input>* CreateUnregistered();

    // 靜態工廠方法 - 創建已註冊的實例（由子類實現）
    // static StageGroupEntity<Output, Input>* CreateRegistered(IPipelineManager<Output, Input>* manager = NULL);
};

template <typename Output, typename Input>
class StageGroup : public IStageGroup<Output, Input> {
private:
    IStageGroup<Output, Input>* m_stageGroup;

protected:
    string m_name;
    
    void SetStageGroup(IStageGroup<Output, Input>* stageGroup) {
        m_stageGroup = stageGroup;
    }

public:
    StageGroup(string name = "StageGroup") {
        m_stageGroup = new StageGroupEntity<Output, Input>(name);
        m_name = name;
    }

    ~StageGroup() {}

    // 是否有流水線階段
    bool HasPipeline(IPipeline<Output, Input>* pipeline) const override
    {
        return m_stageGroup.HasPipeline(pipeline);
    }

    // 添加流水線階段
    bool AddPipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_stageGroup.AddPipeline(pipeline);
    }

    // 移除流水線階段
    bool RemovePipeline(IPipeline<Output, Input>* pipeline) override
    {
        return m_stageGroup.RemovePipeline(pipeline);
    }

    // 清除所有流水線階段
    void ClearPipelines() override
    {
        m_stageGroup.ClearPipelines();
    }

    // 獲取所有流水線階段
    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const override
    {
        return m_stageGroup.GetPipelines(pipelines);
    }

    bool IsError() override
    {
        return m_stageGroup.IsError();
    }

    string LastError() override
    {
        return m_stageGroup.LastError();
    }

    // 獲取是否已註冊到管理器
    bool IsRegistered() const { return m_stageGroup.IsRegistered(); }

    // 設置註冊狀態
    void SetRegistered(bool registered) { m_stageGroup.SetRegistered(registered); }

    // 執行階段群組（由子類實現具體邏輯）
    virtual Output Execute(Input in) override = 0;

    // 獲取階段群組名稱
    virtual string GetName() const override { return m_name; }

    // 靜態工廠方法 - 創建未註冊的實例（由子類實現）
    // static StageGroupEntity<Output, Input>* CreateUnregistered();

    // 靜態工廠方法 - 創建已註冊的實例（由子類實現）
    // static StageGroupEntity<Output, Input>* CreateRegistered(IPipelineManager<Output, Input>* manager = NULL);
};

const bool DECORATORS_ENABLED  = true;
const ENUM_STAGE_GROUP_DECORATOR DECORATORS[] = { STAGE_GROUP_DECORATOR_MSG_HANDLER, STAGE_GROUP_DECORATOR_FILE_LOG };

//+------------------------------------------------------------------+
//| OnInit 階段群組 - 用於實現 EA 初始化階段群組                      |
//+------------------------------------------------------------------+
class OnInitStageGroup : public StageGroup<ENUM_INIT_RETCODE, void*> {
public:
    // 受保護的建構函數 - 允許子類調用
    OnInitStageGroup(string name = "OnInitStageGroup")
    : StageGroup<ENUM_INIT_RETCODE, void*>(name) {
        m_name = name;
        IStageGroup<ENUM_INIT_RETCODE, void*>* stageGroup = new StageGroupEntity<ENUM_INIT_RETCODE, void*>(name);

        // setup decorated stage group
        if(DECORATORS_ENABLED){
            InitStageGroupDecoratorResultBuilder* builder = new InitStageGroupDecoratorResultBuilder();
            builder.SetStageGroup(stageGroup);
            for(int i = 0; i < ArraySize(DECORATORS); i++) {
                builder.SetDecoratorResultFunc(
                    InitStageGroupDecoratorReusltMapper::GetInstance()
                    .GetDecoratorResultFunc(DECORATORS[i])
                );
            }
            StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>* result = builder.Build();
            if(result.IsAvailable()) {
                stageGroup = result.GetDecorator();
            }
            delete builder;
        }

        StageGroup<ENUM_INIT_RETCODE, void*>::SetStageGroup(stageGroup);

    }

    // 靜態工廠方法 - 創建未註冊的實例
    static OnInitStageGroup* CreateUnregistered() {
        return new OnInitStageGroup();
    }

    // 靜態工廠方法 - 創建已註冊的實例
    static OnInitStageGroup* CreateRegistered(IPipelineManager<ENUM_INIT_RETCODE, void*>* manager = NULL) {
        OnInitStageGroup* instance = new OnInitStageGroup();

        if(manager == NULL) {
            manager = OnInitStageManager::GetInstance();
        }

        // 將實例添加到管理器中
        instance.SetRegistered(manager.AddPipeline(instance));
        if(instance.IsRegistered()) {
            Print("階段群組註冊成功");
        } else {
            Print("警告: 階段群組註冊失敗");
        }

        return instance;
    }

    // 執行階段群組
    virtual ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        IPipeline<ENUM_INIT_RETCODE, void*>* pipelines[];
        GetPipelines(pipelines);

        // 執行階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            ENUM_INIT_RETCODE result = pipelines[i].Execute(in);
            if(result != INIT_SUCCEEDED) return result; // 如果任何階段返回非 INIT_SUCCEEDED，則返回該值，表示整個流水線失敗。
        }

        return INIT_SUCCEEDED; // 如果所有階段都返回 INIT_SUCCEEDED，則返回 INIT_SUCCEEDED，表示整個流水線成功。
    }
};

//+------------------------------------------------------------------+
//| OnTick 階段群組 - 用於實現 EA 周期性執行階段群組                  |
//+------------------------------------------------------------------+
class OnTickStageGroup : public StageGroup<bool, void*> {
public:
    // 受保護的建構函數 - 允許子類調用
    OnTickStageGroup(string name = "OnTickStageGroup")
    : StageGroup<bool, void*>(name) {
        m_name = name;
        IStageGroup<bool, void*>* stageGroup = new StageGroupEntity<bool, void*>(name);

        // setup decorated stage group
        if(DECORATORS_ENABLED){
            TickStageGroupDecoratorResultBuilder* builder = new TickStageGroupDecoratorResultBuilder();
            builder.SetStageGroup(stageGroup);
            for(int i = 0; i < ArraySize(DECORATORS); i++) {
                builder.SetDecoratorResultFunc(
                    TickStageGroupDecoratorReusltMapper::GetInstance()
                    .GetDecoratorResultFunc(DECORATORS[i])
                );
            }
            StageGroupDecoratorResult<bool, void*>* result = builder.Build();
            if(result.IsAvailable()) {
                stageGroup = result.GetDecorator();
            }
            delete builder;
        }

        // 將裝飾後的階段群組設置為當前階段群組
        StageGroup<bool, void*>::SetStageGroup(stageGroup);
    }

    // 靜態工廠方法 - 創建未註冊的實例
    static OnTickStageGroup* CreateUnregistered() {
        return new OnTickStageGroup();
    }

    // 靜態工廠方法 - 創建已註冊的實例
    static OnTickStageGroup* CreateRegistered(IPipelineManager<bool, void*>* manager = NULL) {
        OnTickStageGroup* instance = new OnTickStageGroup();

        if(manager == NULL) {
            manager = OnTickStageManager::GetInstance();
        }

        // 將實例添加到管理器中
        instance.SetRegistered(manager.AddPipeline(instance));
        if(instance.IsRegistered()) {
            Print("OnTick階段群組註冊成功");
        } else {
            Print("警告: OnTick階段群組註冊失敗");
        }

        return instance;
    }

    // 執行階段群組
    virtual bool Execute(void* in = NULL) override {
        IPipeline<bool, void*>* pipelines[];
        GetPipelines(pipelines);

        // 執行階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            if(!pipelines[i].Execute(in)) return false; // 如果任何階段返回 false，則返回 false，表示整個流水線失敗。
        }

        return true; // 如果所有階段都返回 true，則返回 true，表示整個流水線成功。
    }
};

//+------------------------------------------------------------------+
//| OnDeinit 階段群組 - 用於實現 EA 終止階段群組                      |
//+------------------------------------------------------------------+
class OnDeinitStageGroup : public StageGroup<bool, int> {
public:
    // 受保護的建構函數 - 允許子類調用
    OnDeinitStageGroup(string name = "OnDeinitStageGroup")
    : StageGroup<bool, int>(name) {
        m_name = name;
        IStageGroup<bool, int>* stageGroup = new StageGroupEntity<bool, int>(name);

        // setup decorated stage group
        if(DECORATORS_ENABLED){
            DeinitStageGroupDecoratorResultBuilder* builder = new DeinitStageGroupDecoratorResultBuilder();
            builder.SetStageGroup(stageGroup);
            for(int i = 0; i < ArraySize(DECORATORS); i++) {
                builder.SetDecoratorResultFunc(
                    DeinitStageGroupDecoratorReusltMapper::GetInstance()
                    .GetDecoratorResultFunc(DECORATORS[i])
                );
            }
            StageGroupDecoratorResult<bool, int>* result = builder.Build();
            if(result.IsAvailable()) {
                stageGroup = result.GetDecorator();
            }
            delete builder;
        }

        // 將裝飾後的階段群組設置為當前階段群組
        StageGroup<bool, int>::SetStageGroup(stageGroup);
    }

public:
    // 靜態工廠方法 - 創建未註冊的實例
    static OnDeinitStageGroup* CreateUnregistered() {
        return new OnDeinitStageGroup();
    }

    // 靜態工廠方法 - 創建已註冊的實例
    static OnDeinitStageGroup* CreateRegistered(IPipelineManager<bool, int>* manager = NULL) {
        OnDeinitStageGroup* instance = new OnDeinitStageGroup();

        if(manager == NULL) {
            manager = OnDeinitStageManager::GetInstance();
        }

        // 將實例添加到管理器中
        instance.SetRegistered(manager.AddPipeline(instance));
        if(instance.IsRegistered()) {
            Print("OnDeinit階段群組註冊成功");
        } else {
            Print("警告: OnDeinit階段群組註冊失敗");
        }

        return instance;
    }

    // 執行階段群組
    virtual bool Execute(int in = 0) override {
        IPipeline<bool, int>* pipelines[];
        GetPipelines(pipelines);

        // 執行階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            bool result = pipelines[i].Execute(in);
            if(!result) {
                // 如果某個階段失敗，可以選擇返回失敗
                // 或繼續執行其他階段
                // 這裡我們選擇繼續執行，但返回失敗狀態
                Print("警告: 階段 ", i, " 失敗");
            }
        }

        return true; // 返回成功
    }
};

typedef OnInitStageGroup* (*OnInitStageGroupFunc)();
typedef OnTickStageGroup* (*OnTickStageGroupFunc)();
typedef OnDeinitStageGroup* (*OnDeinitStageGroupFunc)();
