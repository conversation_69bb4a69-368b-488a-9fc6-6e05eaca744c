@echo off
cd /d "c:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard"
echo Current directory: %CD% > merge_log.txt
echo. >> merge_log.txt
echo Current branch: >> merge_log.txt
git branch >> merge_log.txt
echo. >> merge_log.txt
echo Checking out dev branch... >> merge_log.txt
git checkout dev >> merge_log.txt
echo. >> merge_log.txt
echo Merging feature/PipelineAdvance into dev... >> merge_log.txt
git merge feature/PipelineAdvance >> merge_log.txt
echo. >> merge_log.txt
echo Current branch after merge: >> merge_log.txt
git branch >> merge_log.txt
echo. >> merge_log.txt
echo Done! >> merge_log.txt
echo Merge completed. Check merge_log.txt for details.
