#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"
#include "SignalCheckStage.mqh"
#include "RiskCheckStage.mqh"

// 交易執行階段
class TradeExecutionStage : public OnTickPipeline
{
private:
    string m_symbol;
    int m_magic_number;
    double m_lot_size;
    int m_slippage;
    double m_stop_loss_pips;
    double m_take_profit_pips;
    SignalCheckStage* m_signal_stage;
    RiskCheckStage* m_risk_stage;
    int m_ticket;

public:
    // 建構函數
    TradeExecutionStage(SignalCheckStage* signal_stage,
                        RiskCheckStage* risk_stage,
                        string symbol = NULL,
                        int magic_number = 0,
                        double lot_size = 0.01,
                        int slippage = 3,
                        double stop_loss_pips = 20.0,
                        double take_profit_pips = 40.0)
        : m_signal_stage(signal_stage),
          m_risk_stage(risk_stage),
          m_symbol(symbol == NULL ? Symbol() : symbol),
          m_magic_number(magic_number),
          m_lot_size(lot_size),
          m_slippage(slippage),
          m_stop_loss_pips(stop_loss_pips),
          m_take_profit_pips(take_profit_pips),
          m_ticket(-1)
    {
    }

    // 獲取訂單號
    int GetTicket() const
    {
        return m_ticket;
    }

    bool Execute(void* in = NULL) override
    {
        // 檢查信號階段和風險階段是否有效
        if(m_signal_stage == NULL || m_risk_stage == NULL)
        {
            Print("信號檢查階段或風險檢查階段未設置");
            return false;
        }

        // 如果沒有信號或風險不允許，則不執行交易
        if(!m_signal_stage.HasSignal() || !m_risk_stage.IsRiskAllowed())
        {
            Print("無交易信號或風險不允許，不執行交易");
            return true;
        }

        // 獲取信號類型
        int signal_type = m_signal_stage.GetSignalType();

        // 獲取市場數據
        double bid = MarketInfo(m_symbol, MODE_BID);
        double ask = MarketInfo(m_symbol, MODE_ASK);
        double point = MarketInfo(m_symbol, MODE_POINT);
        int digits = (int)MarketInfo(m_symbol, MODE_DIGITS);

        // 計算止損和止盈價格
        double stop_loss = 0.0;
        double take_profit = 0.0;

        if(signal_type == SIGNAL_BUY)
        {
            stop_loss = bid - m_stop_loss_pips * 10 * point;
            take_profit = bid + m_take_profit_pips * 10 * point;
        }
        else if(signal_type == SIGNAL_SELL)
        {
            stop_loss = ask + m_stop_loss_pips * 10 * point;
            take_profit = ask - m_take_profit_pips * 10 * point;
        }

        // 執行交易
        m_ticket = -1;

        if(signal_type == SIGNAL_BUY)
        {
            m_ticket = OrderSend(m_symbol, OP_BUY, m_lot_size, ask, m_slippage, stop_loss, take_profit,
                              "EA_Wizard Buy", m_magic_number, 0, clrGreen);
        }
        else if(signal_type == SIGNAL_SELL)
        {
            m_ticket = OrderSend(m_symbol, OP_SELL, m_lot_size, bid, m_slippage, stop_loss, take_profit,
                              "EA_Wizard Sell", m_magic_number, 0, clrRed);
        }

        // 檢查交易結果
        if(m_ticket < 0)
        {
            int error_code = GetLastError();
            Print("交易執行失敗 - 錯誤代碼: ", error_code, ", 錯誤描述: ", ErrorDescription(error_code));
            return false;
        }

        // 輸出交易執行結果
        Print("交易執行成功 - 交易品種: ", m_symbol,
              ", 訂單類型: ", (signal_type == SIGNAL_BUY ? "買入" : "賣出"),
              ", 訂單號: ", m_ticket,
              ", 交易量: ", DoubleToString(m_lot_size, 2),
              ", 止損: ", DoubleToString(stop_loss, digits),
              ", 止盈: ", DoubleToString(take_profit, digits));

        return true;
    }
};
