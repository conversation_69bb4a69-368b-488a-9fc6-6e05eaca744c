# Pipeline 模組序列圖

## 基本流水線執行序列

```mermaid
sequenceDiagram
    participant Client
    participant Manager as PipelineManager
    participant Stage1 as 階段1
    participant Stage2 as 階段2
    participant Stage3 as 階段3
    participant Registry as Registry

    Client->>Manager: 創建管理器
    Manager->>Registry: 獲取註冊器
    Client->>Stage1: 創建階段1
    Client->>Stage2: 創建階段2
    Client->>Stage3: 創建階段3
    Client->>Manager: 添加階段1
    Client->>Manager: 添加階段2
    Client->>Manager: 添加階段3
    Client->>Manager: Execute()
    Manager->>Stage1: Execute(input)
    Stage1-->>Manager: 返回結果
    Manager->>Stage2: Execute(input)
    Stage2-->>Manager: 返回結果
    Manager->>Stage3: Execute(input)
    Stage3-->>Manager: 返回結果
    Manager-->>Client: 返回最終結果
    Client->>Manager: 釋放資源
```

## 帶有註冊器的流水線執行序列

```mermaid
sequenceDiagram
    participant Client
    participant Manager as PipelineManager
    participant Stage1 as 階段1
    participant Stage2 as 階段2(帶註冊器)
    participant Stage3 as 階段3(帶註冊器)
    participant Registry as Registry

    Client->>Manager: 創建管理器
    Manager->>Registry: 創建註冊器
    Client->>Stage1: 創建階段1
    Client->>Stage2: 創建階段2(傳入管理器)
    Client->>Stage3: 創建階段3(傳入管理器)
    Client->>Manager: 添加階段1
    Client->>Manager: 添加階段2
    Client->>Manager: 添加階段3
    Client->>Manager: Execute()
    Manager->>Stage1: Execute(input)
    Stage1-->>Manager: 返回結果
    Manager->>Stage2: Execute(input)
    Stage2->>Registry: 註冊數據
    Registry-->>Stage2: 返回註冊結果
    Stage2-->>Manager: 返回結果
    Manager->>Stage3: Execute(input)
    Stage3->>Registry: 獲取數據
    Registry-->>Stage3: 返回數據
    Stage3-->>Manager: 返回結果
    Manager-->>Client: 返回最終結果
    Client->>Manager: 釋放資源
```

## OnInit 流水線執行序列

```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as InitPipelineProcessor
    participant Stage1 as OnInitStartStage
    participant Stage2 as ParameterReadStage
    participant Stage3 as VariableInitStage
    participant Stage4 as IndicatorInitStage
    participant Stage5 as OnInitEndStage

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 OnInitStartStage
    Processor->>Stage2: 添加 ParameterReadStage
    Processor->>Stage3: 添加 VariableInitStage
    Processor->>Stage4: 添加 IndicatorInitStage
    Processor->>Stage5: 添加 OnInitEndStage
    EA->>Processor: Execute()
    Processor->>Stage1: Execute(NULL)
    Stage1-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage2: Execute(NULL)
    Stage2-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage3: Execute(NULL)
    Stage3-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage4: Execute(NULL)
    Stage4-->>Processor: 返回 INIT_SUCCEEDED
    Processor->>Stage5: Execute(NULL)
    Stage5-->>Processor: 返回 INIT_SUCCEEDED
    Processor-->>EA: 返回 INIT_SUCCEEDED
    EA->>Processor: 釋放資源
```

## OnTick 流水線執行序列

```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as TickPipelineProcessor
    participant Stage1 as OnTickStartStage
    participant Stage2 as MarketDataStage
    participant Stage3 as PositionCheckStage
    participant Stage4 as SignalCheckStage
    participant Stage5 as RiskCheckStage
    participant Stage6 as TradeExecutionStage
    participant Stage7 as OnTickEndStage

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 OnTickStartStage
    Processor->>Stage2: 添加 MarketDataStage
    Processor->>Stage3: 添加 PositionCheckStage
    Processor->>Stage4: 添加 SignalCheckStage
    Processor->>Stage5: 添加 RiskCheckStage
    Processor->>Stage6: 添加 TradeExecutionStage
    Processor->>Stage7: 添加 OnTickEndStage
    EA->>Processor: Execute()
    Processor->>Stage1: Execute(NULL)
    Stage1-->>Processor: 返回 true
    Processor->>Stage2: Execute(NULL)
    Stage2-->>Processor: 返回 true
    Processor->>Stage3: Execute(NULL)
    Stage3-->>Processor: 返回 true
    Processor->>Stage4: Execute(NULL)
    Stage4-->>Processor: 返回 true
    Processor->>Stage5: Execute(NULL)
    Stage5-->>Processor: 返回 true
    Processor->>Stage6: Execute(NULL)
    Stage6-->>Processor: 返回 true
    Processor->>Stage7: Execute(NULL)
    Stage7-->>Processor: 返回 true
    Processor-->>EA: 返回 true
    EA->>Processor: 釋放資源
```

## OnDeinit 流水線執行序列

```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as DeinitPipelineProcessor
    participant Stage1 as CleanupStage
    participant Stage2 as LoggingStage
    participant Registry as Registry

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 CleanupStage
    Processor->>Stage2: 添加 LoggingStage
    EA->>Processor: Execute(reason)
    Processor->>Stage1: Execute(reason)
    Stage1->>Registry: 清理註冊器
    Registry-->>Stage1: 返回清理結果
    Stage1-->>Processor: 返回 true
    Processor->>Stage2: Execute(reason)
    Stage2-->>Processor: 返回 true
    Processor-->>EA: 返回 true
    EA->>Processor: 釋放資源
```
