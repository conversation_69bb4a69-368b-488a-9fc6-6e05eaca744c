# PipelineAdvance 模組類別圖 (v1)

PipelineAdvance 模組實現了組合模式的流水線處理架構，允許將複雜的處理邏輯分解為一系列獨立的階段，每個階段專注於特定的任務，從而提高代碼的可維護性和可擴展性。

## 核心概念

- **IPipeline**: 流水線介面，定義了基本的流水線操作
- **Pipeline**: 抽象基類，實現了 IPipeline 介面的基本功能
- **PipelineComposite**: 組合類，實現了組合模式，可以包含多個子流水線
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 類別圖

```mermaid
classDiagram
    %% 核心介面和類別
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        +PipelineResult(bool success, string message, string source)
        +bool IsSuccess()
        +string GetMessage()
        +string GetSource()
    }

    class IPipeline {
        <<interface>>
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    class Pipeline {
        <<abstract>>
        -string m_name
        -string m_type
        -PipelineResult* m_result
        -bool m_executed
        +Pipeline(string name, string type)
        +~Pipeline()
        +virtual void Execute() = 0
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        #void SetResult(bool success, string message, string source)
    }

    class PipelineComposite {
        -string m_name
        -string m_type
        -Vector~IPipeline*~ m_children
        -PipelineResult* m_result
        -bool m_executed
        +PipelineComposite(string name, string type)
        +~PipelineComposite()
        +PipelineResult* Add(IPipeline* child)
        +PipelineResult* Remove(IPipeline* child)
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        -void SetResult(bool success, string message, string source)
    }

    %% 具體流水線類型
    class DataFeedPipeline {
        +DataFeedPipeline(string name)
    }
    note for DataFeedPipeline "PIPELINE_DATAFEED = 'DataFeedPipeline'"

    class SignalPipeline {
        +SignalPipeline(string name)
    }
    note for SignalPipeline "PIPELINE_SIGNAL = 'SignalPipeline'"

    class OrderPipeline {
        +OrderPipeline(string name)
    }
    note for OrderPipeline "PIPELINE_ORDER = 'OrderPipeline'"

    class RiskPipeline {
        +RiskPipeline(string name)
    }
    note for RiskPipeline "PIPELINE_RISK = 'RiskPipeline'"

    class LogPipeline {
        +LogPipeline(string name)
    }
    note for LogPipeline "PIPELINE_LOG = 'LogPipeline'"

    class ErrorPipeline {
        +ErrorPipeline(string name)
    }
    note for ErrorPipeline "PIPELINE_ERROR = 'ErrorPipeline'"

    %% 關係
    IPipeline --> PipelineResult : uses
    IPipeline <|.. Pipeline : implements
    IPipeline <|.. PipelineComposite : implements
    PipelineComposite <|-- DataFeedPipeline : extends
    PipelineComposite <|-- SignalPipeline : extends
    PipelineComposite <|-- OrderPipeline : extends
    PipelineComposite <|-- RiskPipeline : extends
    PipelineComposite <|-- LogPipeline : extends
    PipelineComposite <|-- ErrorPipeline : extends
    PipelineComposite o-- IPipeline : contains
```

## 流水線執行流程

1. **客戶端創建流水線**：客戶端創建具體的流水線實例，如 DataFeedPipeline、SignalPipeline 等
2. **添加子流水線**：對於組合流水線，客戶端可以添加多個子流水線
3. **執行流水線**：客戶端調用流水線的 Execute 方法
4. **處理結果**：客戶端獲取流水線的執行結果，並根據結果進行相應處理

## 使用示例

```mq4
// 創建數據饋送流水線
DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據");

// 創建信號流水線
SignalPipeline* signal = new SignalPipeline("交易信號");

// 創建訂單流水線
OrderPipeline* order = new OrderPipeline("訂單處理");

// 創建風險控制流水線
RiskPipeline* risk = new RiskPipeline("風險控制");

// 創建日誌流水線
LogPipeline* log = new LogPipeline("日誌記錄");

// 創建錯誤處理流水線
ErrorPipeline* error = new ErrorPipeline("錯誤處理");

// 創建主流水線
PipelineComposite* mainPipeline = new PipelineComposite("主流水線", "Main");

// 添加子流水線
mainPipeline.Add(dataFeed);
mainPipeline.Add(signal);
mainPipeline.Add(order);
mainPipeline.Add(risk);
mainPipeline.Add(log);
mainPipeline.Add(error);

// 執行流水線
mainPipeline.Execute();

// 獲取結果
PipelineResult* result = mainPipeline.GetResult();
if(result.IsSuccess())
{
    Print("流水線執行成功: ", result.GetMessage());
}
else
{
    Print("流水線執行失敗: ", result.GetMessage(), ", 來源: ", result.GetSource());
}

// 清理資源
delete mainPipeline; // 這將自動清理所有子流水線
```
