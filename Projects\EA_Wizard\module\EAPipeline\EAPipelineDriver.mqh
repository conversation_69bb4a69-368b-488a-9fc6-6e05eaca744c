#property strict

#include "components/EAPipelineManager.mqh"
#include "components/EARegistry.mqh"
#include "components/EACompoundPipeline.mqh"
#include "enum/EAPipelineEnum.mqh"
#include "components/EAFileLog.mqh"
#include "components/decorator/EALogCompoundPipeline.mqh"
#include "components/decorator/EAErrorHandlingCompoundPipeline.mqh"
#include "components/builder/EACompoundPipelineDirector.mqh"

//+------------------------------------------------------------------+
//| EAPipelineDriver class                                           |
//| Responsible for driving the EAPipelineManager                    |
//+------------------------------------------------------------------+
class EAPipelineDriver
{
private:
    static EAPipelineDriver* s_instance;
    bool m_isInitialized;
    EAPipelineManager* m_manager;
    EARegistry* m_registry;
    EAFileLog* m_logger;
    EAErrorHandler* m_error_handler;

    // 流水線組合對象
    EACompoundPipeline* m_oninit_pipeline;
    EACompoundPipeline* m_ontick_pipeline;
    EACompoundPipeline* m_ondeinit_pipeline;

    // 通用流水線初始化
    void PipelineInit(ENUM_EA_SUB_EVENT &sub_events[], string &decorators[])
    {
        EACompoundPipelineAssistingDirector* 
            main_director = EACompoundPipelineAssistingDirector::GetInstance();
        EABuiltPipelineItem* 
            main_item = main_director.ModifyCompoundPipeline(m_oninit_pipeline, decorators);
        EACompoundPipeline* 
            main_pipeline = dynamic_cast<EACompoundPipeline*>(main_item.GetValue());
        if(main_pipeline == NULL)
        {
            m_logger.Log(CRITICAL, main_pipeline.GetName() + "流水線建立失敗: " + main_item.GetMessage());
            m_isInitialized = false;
            return;
        }
        if(!main_item.IsCompletedBuilt())
        {
            m_logger.Log(WARNING, main_pipeline.GetName() + "流水線裝飾失敗: " + main_item.GetMessage());
        }

        EACompoundPipelineDirector* 
            sub_director = EACompoundPipelineDirector::GetInstance();
        for(int i = 0; i < ArraySize(sub_events); i++)
        {
            EABuiltPipelineItem* 
                sub_item = sub_director.CreateCompoundPipeline(sub_events[i], decorators);
            EACompoundPipeline* 
                sub_pipeline = dynamic_cast<EACompoundPipeline*>(sub_item.GetValue());
            if(sub_pipeline == NULL)
            {
                m_logger.Log(CRITICAL, main_pipeline.GetName() + "流水線建立失敗: " + sub_item.GetMessage());
                m_isInitialized = false;
                return;
            }
            if(!sub_item.IsCompletedBuilt()){
                m_logger.Log(ERROR, main_pipeline.GetName() + "流水線建立失敗: " + sub_item.GetMessage());
                m_isInitialized = false;
            }
            main_pipeline.Add(sub_pipeline);
            m_logger.Log(INFO, main_pipeline.GetName() + "流水線建立成功: " + sub_item.GetMessage());
        }
    }

    // 初始化OnInit流水線
    void OnInitPipelineInit()
    {
        string decorators[] = { 
                                "EALogCompoundPipeline", 
                                "EAErrorHandlingCompoundPipeline"
                              };
        ENUM_EA_SUB_EVENT sub_events[] = {
                                        ONINIT_START,
                                        ONINIT_PARAMETER_READ,
                                        ONINIT_VARIABLE_INIT,
                                        ONINIT_TRADING_ENV_CHECK,
                                        ONINIT_INDICATOR_INIT,
                                        ONINIT_END
                                     };
        
        PipelineInit(sub_events, decorators);
    }

    // 初始化OnTick流水線
    void OnTickPipelineInit()
    {
        string decorators[] = { 
                                "EALogCompoundPipeline", 
                                "EAErrorHandlingCompoundPipeline"
                              };
        ENUM_EA_SUB_EVENT sub_events[] = {
                                        ONTICK_DATAFEED,
                                        ONTICK_SIGNAL,
                                        ONTICK_ORDER,
                                        ONTICK_RISK,
                                        ONTICK_LOG,
                                        ONTICK_ERROR
                                     };

        PipelineInit(sub_events, decorators);
    }

    // 初始化OnDeinit流水線
    void OnDeinitPipelineInit()
    {
        string decorators[] = { 
                                "EALogCompoundPipeline", 
                                "EAErrorHandlingCompoundPipeline"
                              };
        ENUM_EA_SUB_EVENT sub_events[] = {
                                        ONDEINIT_CLEANUP,
                                        ONDEINIT_LOGGING
                                     };

        PipelineInit(sub_events, decorators);
    }

    // 初始化EAPipelineManager
    void EAPipelineManagerInit()
    {
        if(m_manager != NULL)
        {
            m_manager.AddPipeline(m_oninit_pipeline);
            m_manager.AddPipeline(m_ontick_pipeline);
            m_manager.AddPipeline(m_ondeinit_pipeline);
        }
    }

    void RegisterEAFileLog()
    {
        RegistryResult<string>* result = m_registry.Register("EALogger", "日誌記錄器", m_logger);
        if(!result.IsSuccess())
        {
            m_logger.Log(ERROR, "註冊日誌記錄器失敗: " + result.GetMessage());
            m_isInitialized = false;
        }
        else
        {
            m_logger.Log(INFO, "註冊日誌記錄器成功: " + result.GetMessage());
        }
    }

    EAPipelineDriver(EAPipelineManager* manager = NULL, EARegistry* registry = NULL, EAFileLog* logger = NULL, EAErrorHandler* error_handler = NULL)
    : m_isInitialized(true),
      m_manager(manager?manager:EAPipelineManager::GetInstance()),
      m_registry(registry?registry:EARegistry::GetInstance()),
      m_oninit_pipeline(new EACompoundPipeline(EnumToString(ONINIT))),
      m_ontick_pipeline(new EACompoundPipeline(EnumToString(ONTICK))),
      m_ondeinit_pipeline(new EACompoundPipeline(EnumToString(ONDEINIT))),
      m_logger(logger?logger:EAFileLog::GetInstance()),
      m_error_handler(error_handler?error_handler:EAErrorHandler::GetInstance())
    {
        m_logger.Log(INFO, "EAPipelineDriver 初始化開始");

        // 初始化流水線
        OnInitPipelineInit();
        OnTickPipelineInit();
        OnDeinitPipelineInit();

        // 初始化管理器
        EAPipelineManagerInit();

        // 註冊日誌記錄器到註冊器中
        RegisterEAFileLog();
        
        if(m_isInitialized)
            m_logger.Log(INFO, "EAPipelineDriver 初始化成功");
        else
            m_logger.Log(ERROR, "EAPipelineDriver 初始化失敗");
    }

public:
    static EAPipelineDriver* GetInstance()
    {
        if (s_instance == NULL)
        {
            s_instance = new EAPipelineDriver();
        }
        return s_instance;
    }

    ~EAPipelineDriver()
    {
        // 這裡不需要刪除流水線，因為它們已經註冊到EAPipelineManager中
        // 當EAPipelineManager被刪除時，它會清理所有註冊的流水線
        m_logger.Log(INFO, "EAPipelineDriver 刪除成功");
    }

    // Drive OnInit pipeline
    ENUM_INIT_RETCODE OnInit()
    {
        if (!m_isInitialized) return INIT_FAILED;
        PipelineResult* result = m_manager.ExecutePipeline(EnumToString(ONINIT));
        
        if(result != NULL)
        {
            m_logger.Log(DEBUG, "OnInit流水線執行結果: " + (result.IsSuccess() ? "成功" : "失敗") + "\r\n" + " 訊息: " + result.GetMessage());
        }
        if(!result.IsSuccess())
        {
            m_logger.Log(ERROR, "OnInit流水線執行失敗: " + result.GetMessage());
        }
        bool isParameterError = result.GetSource() != EnumToString(ONINIT_PARAMETER_READ)
                                ? false
                                : !result.IsSuccess();
        bool isGeneralError = !result.IsSuccess();

        return isParameterError ? INIT_PARAMETERS_INCORRECT
                                : isGeneralError
                                ? INIT_FAILED
                                : INIT_SUCCEEDED;
    }

    // Drive OnTick pipeline
    void OnTick()
    {
        if (m_manager != NULL) m_manager.ExecutePipeline(EnumToString(ONTICK));
    }

    // Drive OnDeinit pipeline
    void OnDeinit(const int reason)
    {
        m_logger.Log(INFO, "EA 停止，原因: " + (string)reason);
        if (m_manager != NULL) m_manager.ExecutePipeline(EnumToString(ONDEINIT));
    }
};

EAPipelineDriver* EAPipelineDriver::s_instance = EAPipelineDriver::GetInstance();