//+------------------------------------------------------------------+
//|                                                   IPipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../model/PipelineResult.mqh"

//+------------------------------------------------------------------+
//| 流水線介面 - 定義流水線的基本功能                                |
//+------------------------------------------------------------------+
interface IPipeline
{
public:
    // 執行流水線
    void Execute();

    // 獲取流水線名稱
    string GetName();

    // 獲取流水線類型
    string GetType();

    // 獲取流水線執行結果
    PipelineResult* GetResult();

    // 重置流水線狀態
    void Restore();

    // 檢查流水線是否已執行
    bool IsExecuted();
};