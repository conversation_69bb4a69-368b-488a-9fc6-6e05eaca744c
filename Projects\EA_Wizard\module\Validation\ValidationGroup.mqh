#property strict

#include <../../../src/mql4-lib-master/Collection/Vector.mqh>
#include "IValidator.mqh"
#include "ValidationConfig.mqh"

//+------------------------------------------------------------------+
//| 驗證群組類                                                         |
//| 用於管理和執行一組相關的驗證器                                       |
//+------------------------------------------------------------------+
class CValidationGroup
{
private:
    Vector<IValidator*>* m_validators;   // 驗證器集合
    string m_name;                       // 驗證群組名稱
    string m_lastError;                  // 最後的錯誤訊息
    bool m_stopOnFirstError;             // 是否在第一個錯誤時停止

public:
    // 建構函數
    CValidationGroup(const string name = "DefaultValidationGroup")
        : m_name(name)
        , m_lastError("")
        , m_stopOnFirstError(true)
    {
        m_validators = new Vector<IValidator*>(true);  // true 表示 Vector 擁有其元素的所有權
    }
    
    // 解構函數
    ~CValidationGroup()
    {
        if(m_validators != NULL)
        {
            delete m_validators;
            m_validators = NULL;
        }
    }
    
    // 添加驗證器
    bool AddValidator(IValidator* validator)
    {
        if(validator == NULL)
        {
            SetLastError("無效的驗證器");
            return false;
        }
        
        return m_validators.add(validator);
    }
    
    // 移除驗證器
    bool RemoveValidator(IValidator* validator)
    {
        if(validator == NULL)
        {
            SetLastError("無效的驗證器");
            return false;
        }
        
        for(int i = 0; i < m_validators.size(); i++)
        {
            if(m_validators[i] == validator)
            {
                m_validators.remove(i);
                return true;
            }
        }
        
        return false;
    }
    
    // 清空所有驗證器
    void ClearValidators()
    {
        m_validators.clear();
    }
    
    // 獲取驗證器數量
    int GetValidatorCount() const
    {
        return m_validators.size();
    }
    
    // 獲取指定索引的驗證器
    IValidator* GetValidator(const int index)
    {
        if(index < 0 || index >= m_validators.size())
        {
            return NULL;
        }
        
        return m_validators[index];
    }
    
    // 執行所有驗證
    CValidationResult* ValidateAll()
    {
        CValidationResult* result = new CValidationResult();
        
        for(int i = 0; i < m_validators.size(); i++)
        {
            if(m_validators[i].IsEnabled())
            {
                CValidationResult* tempResult = m_validators[i].Validate();
                if(!tempResult.IsValid())
                {
                    result.Merge(tempResult);
                    SetLastError(tempResult.GetMessage());
                    delete tempResult;
                    
                    if(m_stopOnFirstError)
                    {
                        return result;  // 如果設置為在第一個錯誤時停止，則立即返回
                    }
                }
                else
                {
                    delete tempResult;
                }
            }
        }
        
        return result;
    }
    
    // 獲取最後的錯誤訊息
    string GetLastError() const
    {
        return m_lastError;
    }
    
    // 獲取驗證群組名稱
    string GetName() const
    {
        return m_name;
    }
    
    // 設置驗證群組名稱
    void SetName(const string name)
    {
        m_name = name;
    }
    
    // 設置是否在第一個錯誤時停止
    void SetStopOnFirstError(const bool stop)
    {
        m_stopOnFirstError = stop;
    }
    
    // 獲取是否在第一個錯誤時停止
    bool GetStopOnFirstError() const
    {
        return m_stopOnFirstError;
    }

private:
    // 設置最後的錯誤訊息
    void SetLastError(const string error)
    {
        m_lastError = error;
    }
};
