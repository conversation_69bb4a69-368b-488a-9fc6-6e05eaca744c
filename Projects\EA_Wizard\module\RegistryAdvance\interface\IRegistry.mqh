//+------------------------------------------------------------------+
//|                                                  IRegistry.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../model/RegistryItem.mqh"

//+------------------------------------------------------------------+
//| 註冊器接口 - 定義註冊器的基本功能                               |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
interface IRegistry
{
    // 註冊新項目
    RegistryResult<Key>* Register(const string name, const string description, Val value);

    // 移除項目
    bool Unregister(const Key key);

    // 清空註冊器
    void Clear();

    // 獲取註冊器名稱
    string GetName();

    // 獲取項目數量
    int GetCount();

    // 獲取最大項目數量
    int GetMaxItems();

    // 根據Key獲取項目
    RegistryItem<Val>* GetItem(const Key key);

    // 獲取所有Key
    int GetAllKeys(Key &Keys[]);

    // 獲取最後註冊的鍵
    Key GetLastRegisteredKey();
};
