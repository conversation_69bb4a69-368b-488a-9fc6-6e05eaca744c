```mermaid
classDiagram
    class Client
    class PipelineResult {
        +bool success
        +string message
        +string source
        +PipelineResult(bool success, string message, string source)
        +bool IsSuccess()
        +string GetMessage()
        +string GetSource()
    }

    class IPipeline {
        <<interface>>
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    Client --> PipelineResult
    IPipeline --> PipelineResult

    class Pipeline {
        <<abstract>>
        -string name
        -string type
        +Pipeline(string name, string type)
        +virtual void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    class PipelineComposite {
        -string name
        -string type
        -IPipeline* children[]
        +PipelineComposite(string name, string type)
        +PipelineResult* Add(IPipeline* child)
        +PipelineResult* Remove(IPipeline* child)
        +IPipeline* GetChildren()
        +override void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    class DataFeedPipeline {
        <<instance>>
    }
    note for DataFeedPipeline "PIPELINE_DATAFEED = 'DataFeedPipeline'"

    class SignalPipeline {
        <<instance>>
    }
    note for SignalPipeline "PIPELINE_SIGNAL = 'SignalPipeline'"

    class OrderPipeline {
        <<instance>>
    }
    note for OrderPipeline "PIPELINE_ORDER = 'OrderPipeline'"

    class RiskPipeline {
        <<instance>>
    }
    note for RiskPipeline "PIPELINE_RISK = 'RiskPipeline'"

    class LogPipeline {
        <<instance>>
    }
    note for LogPipeline "PIPELINE_LOG = 'LogPipeline'"

    class ErrorPipeline {
        <<instance>>
    }
    note for ErrorPipeline "PIPELINE_ERROR = 'ErrorPipeline'"

    Client --> IPipeline
    IPipeline <|.. Pipeline
    IPipeline <|.. PipelineComposite
    PipelineComposite <|-- DataFeedPipeline
    PipelineComposite <|-- SignalPipeline
    PipelineComposite <|-- OrderPipeline
    PipelineComposite <|-- RiskPipeline
    PipelineComposite <|-- LogPipeline
    PipelineComposite <|-- ErrorPipeline
    PipelineComposite o-- IPipeline : children
```
