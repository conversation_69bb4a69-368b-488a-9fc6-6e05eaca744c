#property strict

#include "components/EACompoundPipeline.mqh"
#include "components/EAPipelineManager.mqh"
#include "enum/EAPipelineEnum.mqh"
#include "components/EAPipelineExplorer.mqh"
#include "components/EARegistry.mqh"
#include "components/decorator/EALogPipeline.mqh"
#include "components/decorator/EALogRegistry.mqh"
#include "components/decorator/EAErrorHandlingRegistry.mqh"
#include "components/builder/EACompoundPipelineDirector.mqh"


const string EA_PIPELINE_TYPE = "EAPipeline";

//+------------------------------------------------------------------+
//| EAPipeline 類 - 單例模式實現的 Pipeline                           |
//+------------------------------------------------------------------+
class EAPipeline : public Pipeline
{
    
private:
    bool m_isInitialized;

    bool SetupPipeline(ENUM_EA_SUB_EVENT event, string name, string type)
    {   
        bool result = false;

        EAPipelineExplorer* 
            explorer = new EAPipelineExplorer(EAPipelineManager::GetInstance());
        EAPipelineItem* 
            item = explorer.FindByName(EnumToString(event));
        if(item.GetName() != EnumToString(event) || 
           item.GetValue() == NULL
          )
        {
            EAErrorHandler::GetInstance().HandleError("流水線建立失敗: 未找到基礎流水線");
        }
        else
        {
            EACompoundPipelineAssistingDirector* 
                director = EACompoundPipelineAssistingDirector::GetInstance();
            EACompoundPipeline* 
                composite = dynamic_cast<EACompoundPipeline*>(item.GetValue());
            string 
                decorators[] = {
                                "EALogCompoundPipeline", 
                                "EAErrorHandlingCompoundPipeline"
                                };
            EABuiltPipelineItem* 
                built_item = director.ModifyCompoundPipeline(composite, decorators);
            
            if(!built_item.IsBuilt() ||
                dynamic_cast<EACompoundPipeline*>(built_item.GetValue()) == NULL
                )
            {
                EAErrorHandler::GetInstance().HandleError("流水線建立失敗: 輔助流水線建立失敗");
            }
            else
            {
                result = dynamic_cast<EACompoundPipeline*>(built_item.GetValue())
                                                            .Add(&this)
                                                            .IsSuccess();
            }
        }
        return result;
    }

public:
    EAPipeline(ENUM_EA_SUB_EVENT event, string name = "EAPipeline")
        : Pipeline(name, EA_PIPELINE_TYPE),
          m_isInitialized(false)
    {
        m_isInitialized = SetupPipeline(event, name, EA_PIPELINE_TYPE);
        if(!m_isInitialized)
        {
            string error_msg = EAErrorHandler::GetInstance().GetLastError();
            EAFileLog::GetInstance().Log(CRITICAL, error_msg);
        }
        else
        {
            EAFileLog::GetInstance().Log(INFO, name + " 流水線建立成功");
        }
    }

    ~EAPipeline()
    {
    }

    // 註冊到 EA 註冊器
    RegistryResult<string>* Register(const string name, const string description, void* value)
    {
        EARegistryBase* 
            registry = new EALogRegistry
                       (
                            new EAErrorHandlingRegistry
                            (
                                EARegistry::GetInstance(),"EARegistry"
                            )
                        );
        return registry.Register(name, description, value);
    }

    // 從 EA 註冊器中獲取值
    RegistryItem<void*>* GetItem(const string name)
    {
        EARegistryBase* 
            registry = new EALogRegistry
                        (
                            new EAErrorHandlingRegistry
                            (
                                EARegistry::GetInstance(),"EARegistry"
                            )
                        );
        return registry.GetItem(name);
    }
    
    // 執行流水線（由子類實現）
    virtual void Execute() = 0;
};
