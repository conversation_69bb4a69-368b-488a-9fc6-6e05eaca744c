//+------------------------------------------------------------------+
//|                                               StageRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../Registry/StringRegistry.mqh"

//+------------------------------------------------------------------+
//| 階段註冊器類 - 用於註冊和管理階段類型的對象                      |
//+------------------------------------------------------------------+
class StageRegistry : public StringRegistry<void*>
{
private:
    static StageRegistry* s_instance;           // 單例實例
    static bool s_isActivated;                        // 是否激活（靜態成員）

    // 私有構造函數（單例模式）
    StageRegistry()
        : StringRegistry<void*>("StageRegistry", 100)
    {
        this.SetKeyPrefix("SR"); // 設置階段註冊器的字頭
    }

    // 內部方法獲取單例實例
    static StageRegistry* GetInstanceInternal() {
        if(s_instance == NULL) {
            s_instance = new StageRegistry();
            s_isActivated = true;
        }
        return s_instance;
    }

public:
    // 獲取單例實例
    static StageRegistry* GetInstance() {
        return GetInstanceInternal();
    }

    // 獲取單例實例（帶自定義名稱和最大項目數）
    static StageRegistry* GetInstance(const string name, const int maxItems) {
        if(s_instance == NULL) {
            s_instance = GetInstanceInternal();
            s_instance.SetName(name);
            s_instance.SetMaxItems(maxItems);
            s_isActivated = true;
        }
        return s_instance;
    }

    ~StageRegistry() {
    }

    // 初始化靜態成員變量
    static void InitStatic() {
        s_instance = NULL;
    }

    // 註冊階段
    bool RegisterStage(const string stageName, const string stageDescription, void* stageValue, string key = NULL) {
        return this.Register(stageName, stageDescription, stageValue, key);
    }

    // 獲取階段
    void* GetStage(const string stageId, void* defaultValue = NULL) {
        return this.GetValue(stageId, defaultValue);
    }

    // 檢查階段是否存在
    bool HasStage(const string stageId) {
        return this.m_items.contains(stageId);
    }

    // 取消註冊階段
    bool UnregisterStage(const string stageId) {
        return this.Unregister(stageId);
    }

    // 是否激活
    static bool IsActivated() {
        return s_isActivated;
    }
};

// 初始化單例實例
StageRegistry* StageRegistry::s_instance = NULL;

bool StageRegistry::s_isActivated = false;
