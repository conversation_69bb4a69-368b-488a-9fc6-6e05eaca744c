#property strict

#include "../IValidator.mqh"
#include "../ValidationConfig.mqh"

//+------------------------------------------------------------------+
//| 字符串驗證器                                                       |
//| 用於驗證字符串長度是否在指定範圍內                                    |
//+------------------------------------------------------------------+
class CStringValidator : public CBaseValidator
{
private:
    string m_value;       // 要驗證的字符串
    int m_minLength;      // 最小長度
    int m_maxLength;      // 最大長度
    string m_field;       // 欄位名稱

public:
    // 建構函數
    CStringValidator(string value, int minLength, int maxLength, const string field)
        : CBaseValidator(VALIDATOR_NAME_STRING, VALIDATOR_TYPE_STRING)
        , m_value(value)
        , m_minLength(minLength)
        , m_maxLength(maxLength)
        , m_field(field)
    {}

    // 執行驗證
    virtual CValidationResult* Validate() override
    {
        CValidationResult* result = new CValidationResult();
        
        // 獲取字符串長度
        int length = StringLen(m_value);
        
        // 檢查長度是否在範圍內
        if(length < m_minLength || length > m_maxLength)
        {
            result.SetInvalid(
                m_field, 
                StringFormat(VALIDATOR_MSG_STRING_LENGTH, m_minLength, m_maxLength),
                VALIDATOR_SOURCE_DEFAULT,
                VALIDATOR_ERROR_RANGE
            );
        }
        
        return result;
    }
    
    // 設置要驗證的字符串
    void SetValue(const string value)
    {
        m_value = value;
    }
    
    // 獲取要驗證的字符串
    string GetValue() const
    {
        return m_value;
    }
    
    // 設置最小長度
    void SetMinLength(const int minLength)
    {
        m_minLength = minLength;
    }
    
    // 獲取最小長度
    int GetMinLength() const
    {
        return m_minLength;
    }
    
    // 設置最大長度
    void SetMaxLength(const int maxLength)
    {
        m_maxLength = maxLength;
    }
    
    // 獲取最大長度
    int GetMaxLength() const
    {
        return m_maxLength;
    }
    
    // 設置欄位名稱
    void SetField(const string field)
    {
        m_field = field;
    }
    
    // 獲取欄位名稱
    string GetField() const
    {
        return m_field;
    }
};
