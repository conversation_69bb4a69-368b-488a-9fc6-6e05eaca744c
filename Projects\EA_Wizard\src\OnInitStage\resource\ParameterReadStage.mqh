#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"

// 參數讀取階段
class ParameterReadStage : public OnInitPipeline
{
private:
    // 交易參數
    int m_magic_number;
    double m_lot_size;
    int m_slippage;
    double m_stop_loss_pips;
    double m_take_profit_pips;
    
    // 風險管理參數
    double m_max_risk_percent;
    int m_max_positions;
    
    // 指標參數
    int m_ma_fast_period;
    int m_ma_slow_period;
    ENUM_MA_METHOD m_ma_method;
    int m_rsi_period;
    double m_rsi_overbought;
    double m_rsi_oversold;
    
public:
    // 建構函數
    ParameterReadStage(
        // 交易參數
        int magic_number = 12345,
        double lot_size = 0.01,
        int slippage = 3,
        double stop_loss_pips = 20.0,
        double take_profit_pips = 40.0,
        
        // 風險管理參數
        double max_risk_percent = 2.0,
        int max_positions = 5,
        
        // 指標參數
        int ma_fast_period = 10,
        int ma_slow_period = 20,
        ENUM_MA_METHOD ma_method = MODE_SMA,
        int rsi_period = 14,
        double rsi_overbought = 70.0,
        double rsi_oversold = 30.0
    )
        : m_magic_number(magic_number),
          m_lot_size(lot_size),
          m_slippage(slippage),
          m_stop_loss_pips(stop_loss_pips),
          m_take_profit_pips(take_profit_pips),
          m_max_risk_percent(max_risk_percent),
          m_max_positions(max_positions),
          m_ma_fast_period(ma_fast_period),
          m_ma_slow_period(ma_slow_period),
          m_ma_method(ma_method),
          m_rsi_period(rsi_period),
          m_rsi_overbought(rsi_overbought),
          m_rsi_oversold(rsi_oversold)
    {
    }
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 檢查參數是否有效
        if(!ValidateParameters())
        {
            return INIT_PARAMETERS_INCORRECT;
        }
        
        // 將參數註冊到註冊器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        
        // 註冊交易參數
        registry.Register("MagicNumber", "魔術數字", &m_magic_number);
        registry.Register("LotSize", "交易量", &m_lot_size);
        registry.Register("Slippage", "滑點", &m_slippage);
        registry.Register("StopLossPips", "止損點數", &m_stop_loss_pips);
        registry.Register("TakeProfitPips", "止盈點數", &m_take_profit_pips);
        
        // 註冊風險管理參數
        registry.Register("MaxRiskPercent", "最大風險百分比", &m_max_risk_percent);
        registry.Register("MaxPositions", "最大持倉數量", &m_max_positions);
        
        // 註冊指標參數
        registry.Register("MAFastPeriod", "快速移動平均線週期", &m_ma_fast_period);
        registry.Register("MASlowPeriod", "慢速移動平均線週期", &m_ma_slow_period);
        registry.Register("MAMethod", "移動平均線方法", &m_ma_method);
        registry.Register("RSIPeriod", "RSI週期", &m_rsi_period);
        registry.Register("RSIOverbought", "RSI超買水平", &m_rsi_overbought);
        registry.Register("RSIOversold", "RSI超賣水平", &m_rsi_oversold);
        
        Print("參數讀取成功 - 魔術數字: ", m_magic_number, 
              ", 交易量: ", m_lot_size, 
              ", 止損點數: ", m_stop_loss_pips, 
              ", 止盈點數: ", m_take_profit_pips);
        
        return INIT_SUCCEEDED;
    }
    
private:
    // 驗證參數
    bool ValidateParameters()
    {
        // 檢查交易參數
        if(m_magic_number <= 0)
        {
            Print("魔術數字必須大於0");
            return false;
        }
        
        if(m_lot_size <= 0.0)
        {
            Print("交易量必須大於0");
            return false;
        }
        
        if(m_slippage < 0)
        {
            Print("滑點不能為負數");
            return false;
        }
        
        if(m_stop_loss_pips <= 0.0)
        {
            Print("止損點數必須大於0");
            return false;
        }
        
        if(m_take_profit_pips <= 0.0)
        {
            Print("止盈點數必須大於0");
            return false;
        }
        
        // 檢查風險管理參數
        if(m_max_risk_percent <= 0.0 || m_max_risk_percent > 100.0)
        {
            Print("最大風險百分比必須在0-100之間");
            return false;
        }
        
        if(m_max_positions <= 0)
        {
            Print("最大持倉數量必須大於0");
            return false;
        }
        
        // 檢查指標參數
        if(m_ma_fast_period <= 0)
        {
            Print("快速移動平均線週期必須大於0");
            return false;
        }
        
        if(m_ma_slow_period <= 0)
        {
            Print("慢速移動平均線週期必須大於0");
            return false;
        }
        
        if(m_ma_fast_period >= m_ma_slow_period)
        {
            Print("快速移動平均線週期必須小於慢速移動平均線週期");
            return false;
        }
        
        if(m_rsi_period <= 0)
        {
            Print("RSI週期必須大於0");
            return false;
        }
        
        if(m_rsi_overbought <= m_rsi_oversold)
        {
            Print("RSI超買水平必須大於RSI超賣水平");
            return false;
        }
        
        return true;
    }
};
