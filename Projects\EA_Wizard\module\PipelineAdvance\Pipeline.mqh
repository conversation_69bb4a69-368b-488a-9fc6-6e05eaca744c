//+------------------------------------------------------------------+
//|                                                    Pipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "interface/IPipeline.mqh"
#include "model/PipelineResult.mqh"

//+------------------------------------------------------------------+
//| 流水線抽象基類 - 實現 IPipeline 介面的基本功能                   |
//+------------------------------------------------------------------+
class Pipeline : public IPipeline
{
private:
    string m_name;                // 流水線名稱
    string m_type;                // 流水線類型
    PipelineResult* m_result;     // 流水線執行結果
    bool m_executed;              // 是否已執行

public:
    // 構造函數
    Pipeline(string name, string type = "Pipeline")
    : m_name(name), m_type(type), m_executed(false)
    {
        // 初始化結果為默認值
        m_result = new PipelineResult(false, "尚未執行", m_name);
    }

    // 析構函數
    ~Pipeline()
    {
        if(m_result != NULL)
        {
            m_result = NULL;
        }
    }

    // 執行流水線（由子類實現）
    virtual void Execute() = 0;

    // 獲取流水線名稱
    string GetName() override { return m_name; }

    // 獲取流水線類型
    string GetType() override { return m_type; }

    // 獲取流水線執行結果
    PipelineResult* GetResult() override { return m_result; }

    // 重置流水線狀態
    void Restore() override
    {
        if(m_result != NULL)
        {
            m_result = NULL;
        }
        // 初始化結果為默認值
        m_result = new PipelineResult(false, "尚未執行", m_name);
        m_executed = false;
    }

    // 檢查流水線是否已執行
    bool IsExecuted() override { return m_executed; }

protected:
    // 設置流水線執行結果
    void SetResult(bool success, string message, string source = "")
    {
        if(m_result != NULL)
        {
            m_result = NULL;
        }

        if(source == "")
        {
            source = m_name;
        }

        m_result = new PipelineResult(success, message, source);
        m_executed = true;
    }
};