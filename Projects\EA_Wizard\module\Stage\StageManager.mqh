//+------------------------------------------------------------------+
//|                                                 StageManager.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Enum.mqh"
#include "../Pipeline/PipelineManager.mqh"
#include "StageManagerRegistry.mqh"

//+------------------------------------------------------------------+
//| 階段管理器介面 - 定義階段管理器的基本功能                            |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename RegistryVal>
interface IStageManager : public IPipelineManager<Output, Input>
{
public:
    StageManagerRegistry<RegistryVal>* GetRegistry() const;
    string GetId() const;
    string GetName() const;
    string GetDescription() const;
};

//+------------------------------------------------------------------+
//| 階段管理器基類 - 用於管理和執行 EA 階段的模板類                   |
//+------------------------------------------------------------------+
template <typename Output, typename Input, typename RegistryVal>
class StageManager : public IStageManager<Output, Input, RegistryVal> {
private:
    PipelineManager<Output, Input> m_pipeline_manager; // 流水線管理器

protected:
    StageManagerRegistry<RegistryVal>* m_registry; // 註冊器成員，用於存儲共享數據
    string m_id;                      // 註冊器提供的ID
    string m_name;                    // 管理器名稱
    string m_description;             // 管理器描述

    // 受保護的構造函數（模板方法模式）
    StageManager(const string name, const string description)
    : m_pipeline_manager(name, true)
    , m_name(name)
    , m_description(description) {}

    // 初始化註冊器（由子類實現）
    virtual void InitRegistry() = 0;

    // 註冊自身（由子類實現）
    virtual void Register() = 0;

public:
    // 解構函數
    virtual ~StageManager() {
        // 使用記錄的ID取消註冊
        if(m_id != "") {
            m_registry.Unregister(m_id);
            Print("階段管理器取消註冊，ID: ", m_id);
        }
    }

    bool IsError() override {
        return m_pipeline_manager.IsError();
    }

    string LastError() override {
        return m_pipeline_manager.LastError();
    }

    // 是否有流水線階段
    bool HasPipeline(IPipeline<Output, Input>* pipeline) const override {
        return m_pipeline_manager.HasPipeline(pipeline);
    }

    // 添加流水線階段
    bool AddPipeline(IPipeline<Output, Input>* pipeline) override {
        return m_pipeline_manager.AddPipeline(pipeline);
    }

    // 移除流水線階段
    bool RemovePipeline(IPipeline<Output, Input>* pipeline) override {
        return m_pipeline_manager.RemovePipeline(pipeline);
    }

    // 清除所有流水線階段
    void ClearPipelines() override {
        m_pipeline_manager.ClearPipelines();
    }

    // 獲取所有流水線階段
    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const override {
        return m_pipeline_manager.GetPipelines(pipelines);
    }

    // 執行流水線
    Output Execute(Input in = NULL) override {
        return m_pipeline_manager.Execute(in);
    }

    // 設置管理器名稱
    void SetName(const string name) override {
        m_pipeline_manager.SetName(name);
    }

    // 是否在錯誤時停止執行
    bool GetStopOnError() const override {
        return m_pipeline_manager.GetStopOnError();
    }

    // 設置是否在錯誤時停止執行
    void SetStopOnError(bool stopOnError) override {
        m_pipeline_manager.SetStopOnError(stopOnError);
    }

    // 獲取註冊器
    StageManagerRegistry<RegistryVal>* GetRegistry() const {
        return m_registry;
    }

    // 獲取管理器ID
    string GetId() const {
        return m_id;
    }

    // 獲取管理器名稱
    string GetName() const {
        return m_name;
    }

    // 獲取管理器描述
    string GetDescription() const {
        return m_description;
    }
};

//+------------------------------------------------------------------+
//| OnInit 階段管理器 - 用於管理和執行 EA 初始化階段 |
//+------------------------------------------------------------------+
class OnInitStageManager : public StageManager<ENUM_INIT_RETCODE, void*, IPipelineManager<ENUM_INIT_RETCODE, void*>*> {
private:
    static bool s_registered;         // 是否已註冊到註冊器
    static OnInitStageManager* s_instance;  // 單例實例

    // 私有構造函數（單例模式）
    OnInitStageManager()
    : StageManager<ENUM_INIT_RETCODE, void*, IPipelineManager<ENUM_INIT_RETCODE, void*>*>("OnInitStageManager", "EA 初始化階段管理器") {
        InitRegistry();
        Register();
    }

    // 初始化註冊器
    void InitRegistry() override {
        m_registry = StageManagerRegistry<IPipelineManager<ENUM_INIT_RETCODE, void*>*>::GetInstance();
    }

    // 註冊自身
    void Register() override {
        s_registered = m_registry.Register(m_name, m_description, &this);
        if(s_registered) {
            m_id = m_registry.GetLastRegisteredKey();
            Print("階段管理器註冊成功，ID: ", m_id);
        } else {
            Print("警告: 階段管理器註冊失敗");
        }
    }

    // 內部方法獲取單例實例
    static OnInitStageManager* GetInstanceInternal() {
        if(s_instance == NULL) {
            s_instance = new OnInitStageManager();
        }
        return s_instance;
    }

public:
    // 獲取單例實例
    static OnInitStageManager* GetInstance() {
        return GetInstanceInternal();
    }

    // 獲取單例實例（帶初始階段）
    static OnInitStageManager* GetInstance(IPipeline<ENUM_INIT_RETCODE, void*>* &pipelines[]) {
        OnInitStageManager* instance = GetInstanceInternal();

        // 添加階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            instance.AddPipeline(pipelines[i]);
        }

        return instance;
    }

    // 解構函數
    ~OnInitStageManager() {
        s_registered = false;
    }

    // 執行 OnInit 階段
    virtual ENUM_INIT_RETCODE Execute(void* in = NULL) override {
        IPipeline<ENUM_INIT_RETCODE, void*>* pipelines[];
        int count = GetPipelines(pipelines);

        Print("開始執行 EA 初始化階段, 階段數量: ", count);

        // 如果沒有階段，返回成功
        if(count == 0) {
            Print("警告: 沒有初始化階段註冊");
            return INIT_SUCCEEDED;
        }

        // 執行階段
        for(int i = 0; i < count; i++) {
            Print("執行初始化階段 ", i+1, "/", count);
            ENUM_INIT_RETCODE result = pipelines[i].Execute(in);

            if(result != INIT_SUCCEEDED) {
                Print("警告: 初始化階段 ", i+1, " 失敗，代碼: ", result);
                return result; // 如果任何階段返回非 INIT_SUCCEEDED，則返回該代碼，表示整個流水線失敗。
            }
        }

        Print("EA 初始化階段執行成功");
        return INIT_SUCCEEDED; // 如果所有階段都返回 INIT_SUCCEEDED，則返回 INIT_SUCCEEDED，表示整個流水線成功。
    }

    // 是否已註冊
    static bool IsRegistered() { return s_registered; }

    // 判斷結果是否為錯誤
    protected:
    bool IsError(ENUM_INIT_RETCODE result) override {
        return result != INIT_SUCCEEDED;
    }
};

//+------------------------------------------------------------------+
//| OnTick 階段管理器 - 用於管理和執行 EA 周期性執行階段 |
//+------------------------------------------------------------------+
class OnTickStageManager : public StageManager<bool, void*, IPipelineManager<bool, void*>*> {
private:
    static bool s_registered;         // 是否已註冊到註冊器
    static OnTickStageManager* s_instance;  // 單例實例

    // 私有構造函數（單例模式）
    OnTickStageManager()
    : StageManager<bool, void*, IPipelineManager<bool, void*>*>("OnTickStageManager", "EA 周期性執行階段管理器") {
        InitRegistry();
        Register();
    }

    // 初始化註冊器
    void InitRegistry() override {
        m_registry = StageManagerRegistry<IPipelineManager<bool, void*>*>::GetInstance();
    }

    // 註冊自身
    void Register() override {
        s_registered = m_registry.Register(m_name, m_description, &this);
        if(s_registered) {
            m_id = m_registry.GetLastRegisteredKey();
            Print("階段管理器註冊成功，ID: ", m_id);
        } else {
            Print("警告: 階段管理器註冊失敗");
        }
    }

    // 內部方法獲取單例實例
    static OnTickStageManager* GetInstanceInternal() {
        if(s_instance == NULL) {
            s_instance = new OnTickStageManager();
        }
        return s_instance;
    }

public:
    // 獲取單例實例
    static OnTickStageManager* GetInstance() {
        return GetInstanceInternal();
    }

    // 獲取單例實例（帶初始階段）
    static OnTickStageManager* GetInstance(IPipeline<bool, void*>* &pipelines[]) {
        OnTickStageManager* instance = GetInstanceInternal();

        // 添加階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            instance.AddPipeline(pipelines[i]);
        }

        return instance;
    }

    // 解構函數
    ~OnTickStageManager() {
        s_registered = false;
    }

    // 執行 OnTick 階段
    virtual bool Execute(void* in = NULL) override {
        IPipeline<bool, void*>* pipelines[];
        int count = GetPipelines(pipelines);

        // 如果沒有階段，返回成功
        if(count == 0) {
            return true;
        }

        // 執行階段
        for(int i = 0; i < count; i++) {
            bool result = pipelines[i].Execute(in);
            if(!result) {
                Print("警告: 周期性執行階段 ", i+1, " 失敗");
                return false; // 如果任何階段返回 false，則返回 false，表示整個流水線失敗。
            }
        }

        return true; // 如果所有階段都返回 true，則返回 true，表示整個流水線成功。
    }

    // 是否已註冊
    static bool IsRegistered() { return s_registered; }

    // 判斷結果是否為錯誤
    protected:
    bool IsError(bool result) override {
        return !result;
    }
};

//+------------------------------------------------------------------+
//| OnDeinit 階段管理器 - 用於管理和執行 EA 終止階段 |
//+------------------------------------------------------------------+
class OnDeinitStageManager : public StageManager<bool, int, IPipelineManager<bool, int>*> {
private:
    static bool s_registered;         // 是否已註冊到註冊器
    static OnDeinitStageManager* s_instance;  // 單例實例

    // 私有構造函數（單例模式）
    OnDeinitStageManager()
    : StageManager<bool, int, IPipelineManager<bool, int>*>("OnDeinitStageManager", "EA 終止階段管理器") {
        InitRegistry();
        Register();
    }

    // 初始化註冊器
    void InitRegistry() override {
        m_registry = StageManagerRegistry<IPipelineManager<bool, int>*>::GetInstance();
    }

    // 註冊自身
    void Register() override {
        s_registered = m_registry.Register(m_name, m_description, &this);
        if(s_registered) {
            m_id = m_registry.GetLastRegisteredKey();
            Print("階段管理器註冊成功，ID: ", m_id);
        } else {
            Print("警告: 階段管理器註冊失敗");
        }
    }

    // 內部方法獲取單例實例
    static OnDeinitStageManager* GetInstanceInternal() {
        if(s_instance == NULL) {
            s_instance = new OnDeinitStageManager();
        }
        return s_instance;
    }

public:
    // 獲取單例實例
    static OnDeinitStageManager* GetInstance() {
        return GetInstanceInternal();
    }

    // 獲取單例實例（帶初始階段）
    static OnDeinitStageManager* GetInstance(IPipeline<bool, int>* &pipelines[]) {
        OnDeinitStageManager* instance = GetInstanceInternal();

        // 添加階段
        for(int i = 0; i < ArraySize(pipelines); i++) {
            instance.AddPipeline(pipelines[i]);
        }

        return instance;
    }

    // 解構函數
    ~OnDeinitStageManager() {
        s_registered = false;
    }

    // 執行 OnDeinit 階段
    virtual bool Execute(int in = 0) override {
        IPipeline<bool, int>* pipelines[];
        int count = GetPipelines(pipelines);

        Print("開始執行 EA 終止階段, 階段數量: ", count);

        // 如果沒有階段，返回成功
        if(count == 0) {
            Print("警告: 沒有終止階段註冊");
            return true;
        }

        // 執行階段
        bool allSuccess = true;
        for(int i = 0; i < count; i++) {
            Print("執行終止階段 ", i+1, "/", count);
            bool result = pipelines[i].Execute(in);

            if(!result) {
                // 記錄失敗，但繼續執行其他階段
                allSuccess = false;
                Print("警告: 終止階段 ", i+1, " 失敗");
            }
        }

        if(allSuccess) {
            Print("EA 終止階段執行成功");
        } else {
            Print("EA 終止階段執行完成，但有失敗階段");
        }
        return allSuccess; // 返回整體成功狀態
    }

    // 是否已註冊
    static bool IsRegistered() { return s_registered; }

    // 判斷結果是否為錯誤
    protected:
    bool IsError(bool result) override {
        return !result;
    }
};

// 初始化階段管理器的靜態成員
bool OnInitStageManager::s_registered = false;
OnInitStageManager* OnInitStageManager::s_instance = NULL;

// 初始化周期性執行階段管理器的靜態成員
bool OnTickStageManager::s_registered = false;
OnTickStageManager* OnTickStageManager::s_instance = NULL;

// 初始化終止階段管理器的靜態成員
bool OnDeinitStageManager::s_registered = false;
OnDeinitStageManager* OnDeinitStageManager::s_instance = NULL;
