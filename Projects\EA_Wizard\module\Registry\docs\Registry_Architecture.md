# Registry 模組架構文檔

## 1. 概述

Registry 模組是 EA_Wizard 專案中的核心組件，提供了一個通用的註冊和管理各種類型對象的機制。它採用泛型設計，支持不同類型的鍵和值，並提供了豐富的功能，包括註冊、查詢、更新和刪除項目。

## 2. 核心組件

### 2.1 註冊項目 (RegistryItem)

`RegistryItem` 是註冊器中存儲的基本單元，包含以下屬性：

- **ID**: 唯一標識符
- **名稱**: 項目名稱
- **描述**: 項目描述
- **值**: 項目值（泛型類型）
- **創建時間**: 項目創建時間
- **更新時間**: 項目最後更新時間

### 2.2 註冊器基類 (Registry)

`Registry` 是所有註冊器的抽象基類，定義了註冊器的基本功能：

- **註冊項目**: 將新項目添加到註冊器中
- **查詢項目**: 根據 ID 獲取項目或項目值
- **更新項目**: 更新已註冊項目的值
- **刪除項目**: 從註冊器中移除項目
- **清空註冊器**: 移除所有項目
- **遍歷項目**: 使用回調函數遍歷所有項目

### 2.3 具體註冊器實現

- **StringRegistry**: 使用字符串作為鍵的註冊器
- **IntRegistry**: 使用整數作為鍵的註冊器
- **PointerRegistry**: 使用指針作為鍵的註冊器
- **StageRegistry**: 用於註冊和管理階段對象的特殊註冊器
- **StageManagerRegistry**: 用於註冊和管理階段管理器的特殊註冊器

## 3. 設計模式

### 3.1 單例模式

Registry 模組中的一些註冊器（如 StageRegistry 和 StageManagerRegistry）採用單例模式，確保在整個應用程序中只有一個實例：

```cpp
// 獲取 StageRegistry 單例實例
StageRegistry* registry = StageRegistry::GetInstance();
```

### 3.2 工廠模式

Registry 模組使用工廠方法創建註冊項目：

```cpp
// 創建新項目
IRegistryItem<Val>* item = new RegistryItem<Val>(id, name, description, value);
```

### 3.3 觀察者模式

Registry 模組可以通過回調函數實現觀察者模式，允許客戶端在遍歷項目時執行自定義操作：

```cpp
// 使用回調函數遍歷所有項目
registry.ForEach(PrintItem);
```

## 4. 關鍵流程

### 4.1 註冊流程

1. 檢查是否達到最大項目數量
2. 生成唯一 ID 或使用提供的鍵
3. 檢查鍵是否已存在
4. 創建新項目
5. 添加到哈希表
6. 保存最後註冊的鍵

### 4.2 查詢流程

1. 檢查鍵是否存在
2. 獲取項目
3. 返回項目值或默認值

### 4.3 更新流程

1. 檢查鍵是否存在
2. 獲取項目
3. 設置新值
4. 更新時間戳

### 4.4 刪除流程

1. 檢查鍵是否存在
2. 從哈希表中移除
3. 釋放項目內存

## 5. 與其他模組的交互

### 5.1 與 Pipeline 模組的交互

Registry 模組與 Pipeline 模組緊密集成，用於存儲和管理流水線階段：

```cpp
// 在 TickPipelineProcessor 中使用註冊器
TickPipelineProcessor(int magic_number = 12345, string symbol = NULL) {
    // 設置註冊器的鍵字頭
    GetRegistry().SetKeyPrefix("Trading");
    
    // 添加流水線階段
    AddPipeline(new OnTickStartStage());
    AddPipeline(new MarketDataStage(trade_symbol));
    // ...
}
```

### 5.2 與 Stage 模組的交互

Registry 模組用於註冊和管理 Stage 模組中的階段對象：

```cpp
// 註冊階段
bool RegisterStage(const string stageName, const string stageDescription, void* stageValue, string key = NULL) {
    return this.Register(stageName, stageDescription, stageValue, key);
}

// 獲取階段
void* GetStage(const string stageId, void* defaultValue = NULL) {
    return this.GetValue(stageId, defaultValue);
}
```

## 6. 擴展性

Registry 模組的泛型設計使其具有很高的擴展性，可以註冊和管理各種類型的對象：

- 可以創建新的註冊器類型，支持不同類型的鍵
- 可以註冊不同類型的值，包括基本類型、對象指針等
- 可以通過繼承現有註冊器類型，添加特定領域的功能

## 7. 使用示例

### 7.1 基本用法

```cpp
// 獲取字符串註冊器實例
Registry<string, string>* stringRegistry = Registry<string, string>::GetInstance("StringRegistry");

// 註冊項目
string id = stringRegistry.Register("Symbol", "交易品種", "EURUSD");

// 獲取項目值
string symbol = stringRegistry.GetValue(id);

// 更新項目值
stringRegistry.UpdateValue(id, "GBPUSD");

// 移除項目
stringRegistry.Unregister(id);
```

### 7.2 使用整數鍵

```cpp
// 獲取使用整數作為鍵的註冊器實例
Registry<int, string>* intKeyRegistry = Registry<int, string>::GetInstance("IntKeyRegistry");

// 註冊項目（使用自動生成的鍵）
int id1 = intKeyRegistry.Register("Symbol", "交易品種", "EURUSD");

// 註冊項目（使用指定的鍵）
int customId = 100;
int id2 = intKeyRegistry.Register("TimeFrame", "時間週期", "H1", customId);
```

### 7.3 註冊對象

```cpp
// 獲取對象註冊器實例
Registry<string, CObject*>* objectRegistry = Registry<string, CObject*>::GetInstance("ObjectRegistry");

// 創建並註冊對象
CArrayString* array = new CArrayString();
array.Add("Item1");
array.Add("Item2");

string id = objectRegistry.Register("StringArray", "字符串數組", array);

// 獲取註冊的對象
CArrayString* retrievedArray = dynamic_cast<CArrayString*>(objectRegistry.GetValue(id));
```

## 8. 最佳實踐

- 使用有意義的名稱和描述註冊項目，便於後續管理
- 在不再需要註冊器時，調用 Clear() 方法釋放資源
- 使用 ForEach() 方法遍歷項目，而不是手動獲取所有 ID
- 使用 SetKeyPrefix() 方法設置不同的前綴，避免不同註冊器之間的鍵衝突
- 在註冊對象時，確保在註冊器被清空或刪除時，對象也被正確釋放
