# Pipeline 模組文檔

## 概述

Pipeline 模組是 EA_Wizard 專案的核心組件，實現了流水線設計模式，用於處理 EA 的初始化、交易執行和清理等操作。該模組允許將複雜的處理邏輯分解為一系列獨立的階段，每個階段專注於特定的任務，從而提高代碼的可維護性和可擴展性。

## 文檔目錄

1. [Pipeline 模組概述](./Pipeline_Overview.md)：詳細介紹 Pipeline 模組的核心概念、架構和使用方法
2. [Pipeline 模組類別圖](./Pipeline_ClassDiagram.md)：展示 Pipeline 模組的類別結構和關係
3. [Pipeline 模組流程圖](./Pipeline_FlowChart.md)：展示 Pipeline 模組的執行流程
4. [Pipeline 模組序列圖](./Pipeline_SequenceDiagram.md)：展示 Pipeline 模組的交互序列

## 核心組件

1. **IPipeline**：流水線介面，定義了 `Execute` 方法
2. **PipelineGroupBase**：流水線群組基類，實現了 `IPipeline` 介面，提供了階段管理功能
3. **PipelineManager**：流水線管理器，繼承自 `PipelineGroupBase`，提供了流水線執行和錯誤處理功能
4. **RegistrablePipeline**：可註冊流水線，實現了 `IPipeline` 介面，提供了與註冊器集成的功能
5. **特定流水線類型**：如 `OnInitPipeline`、`OnTickPipeline` 和 `OnDeinitPipeline`，用於處理特定類型的操作
6. **特定流水線管理器**：如 `OnInitPipelineManager`、`OnTickPipelineManager` 和 `OnDeinitPipelineManager`，用於管理特定類型的流水線

## 使用示例

```cpp
// 創建 OnInit 流水線處理器
InitPipelineProcessor* processor = new InitPipelineProcessor(Symbol());

// 執行初始化流水線
ENUM_INIT_RETCODE result = processor.Execute();

// 處理初始化結果
switch (result)
{
case INIT_PARAMETERS_INCORRECT:
    Print("EA初始化失敗，原因: 參數錯誤");
    delete processor;
    return(result);
case INIT_FAILED:
    Print("EA初始化失敗，原因: 初始化失敗");
    delete processor;
    return(result);
}

Print("EA初始化成功");
delete processor;
return(INIT_SUCCEEDED);
```

## 擴展方向

1. **並行執行**：支持階段的並行執行，提高性能
2. **條件執行**：根據條件選擇性地執行某些階段
3. **動態配置**：支持在運行時動態配置流水線
4. **監控和日誌**：添加監控和日誌功能，方便調試和優化
5. **可視化**：提供流水線執行過程的可視化展示
