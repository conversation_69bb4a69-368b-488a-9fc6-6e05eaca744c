//+------------------------------------------------------------------+
//|                                      RegistryAdvancedExample.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Registry.mqh"

//+------------------------------------------------------------------+
//| 進階示例類 - 用於演示註冊器的進階用法                              |
//+------------------------------------------------------------------+
class RegistryAdvancedExample
{
public:
    // 演示如何使用整數作為鍵
    static void DemoIntKeyRegistry()
    {
        // 獲取使用整數作為鍵的註冊器實例
        Registry<int, string>* intKeyRegistry = Registry<int, string>::GetInstance("IntKeyRegistry");

        // 註冊項目（使用自動生成的鍵）
        int id1 = intKeyRegistry.Register("Symbol", "交易品種", "EURUSD");
        int id2 = intKeyRegistry.Register("TimeFrame", "時間週期", "H1");

        // 註冊項目（使用指定的鍵）
        int customId = 100;
        int id3 = intKeyRegistry.Register("Strategy", "策略名稱", "MA Cross", customId);

        // 獲取註冊的值
        string symbol = intKeyRegistry.GetValue(id1);
        Print("註冊的交易品種: ", symbol);

        // 更新值
        intKeyRegistry.UpdateValue(id1, "GBPUSD");
        symbol = intKeyRegistry.GetValue(id1);
        Print("更新後的交易品種: ", symbol);

        // 獲取所有註冊的ID
        int ids[];
        intKeyRegistry.GetAllIds(ids);
        Print("註冊的項目數量: ", ArraySize(ids));

        // 遍歷所有項目
        for(int i = 0; i < ArraySize(ids); i++)
        {
            RegistryItem<string>* item = intKeyRegistry.GetItem(ids[i]);
            if(item != NULL)
            {
                Print("ID: ", ids[i], ", 名稱: ", item.GetName(), ", 值: ", item.GetValue());
            }
        }

        // 使用回調函數遍歷
        intKeyRegistry.ForEach(PrintIntKeyItem);

        // 移除項目
        intKeyRegistry.Unregister(id3);
        Print("移除後的項目數量: ", intKeyRegistry.GetCount());
    }

    // 演示如何使用枚舉作為鍵
    static void DemoEnumKeyRegistry()
    {
        // 獲取使用枚舉作為鍵的註冊器實例
        Registry<ENUM_TIMEFRAMES, double>* timeframeRegistry = Registry<ENUM_TIMEFRAMES, double>::GetInstance("TimeframeRegistry");

        // 註冊不同時間週期的移動平均線週期
        timeframeRegistry.Register("MA Period M1", "M1 時間週期的移動平均線週期", 5.0, PERIOD_M1);
        timeframeRegistry.Register("MA Period M5", "M5 時間週期的移動平均線週期", 10.0, PERIOD_M5);
        timeframeRegistry.Register("MA Period M15", "M15 時間週期的移動平均線週期", 20.0, PERIOD_M15);
        timeframeRegistry.Register("MA Period H1", "H1 時間週期的移動平均線週期", 50.0, PERIOD_H1);
        timeframeRegistry.Register("MA Period H4", "H4 時間週期的移動平均線週期", 100.0, PERIOD_H4);
        timeframeRegistry.Register("MA Period D1", "D1 時間週期的移動平均線週期", 200.0, PERIOD_D1);

        // 獲取註冊的值
        double maPeriodH1 = timeframeRegistry.GetValue(PERIOD_H1);
        Print("H1 時間週期的移動平均線週期: ", maPeriodH1);

        // 更新值
        timeframeRegistry.UpdateValue(PERIOD_H1, 60.0);
        maPeriodH1 = timeframeRegistry.GetValue(PERIOD_H1);
        Print("更新後的 H1 時間週期的移動平均線週期: ", maPeriodH1);

        // 獲取所有註冊的時間週期
        ENUM_TIMEFRAMES timeframes[];
        timeframeRegistry.GetAllIds(timeframes);
        Print("註冊的時間週期數量: ", ArraySize(timeframes));

        // 遍歷所有項目
        for(int i = 0; i < ArraySize(timeframes); i++)
        {
            RegistryItem<double>* item = timeframeRegistry.GetItem(timeframes[i]);
            if(item != NULL)
            {
                Print("時間週期: ", EnumToString(timeframes[i]), ", 名稱: ", item.GetName(), ", 值: ", item.GetValue());
            }
        }

        // 移除項目
        timeframeRegistry.Unregister(PERIOD_M1);
        Print("移除後的項目數量: ", timeframeRegistry.GetCount());
    }

private:
    // 用於遍歷的回調函數（整數鍵）
    static void PrintIntKeyItem(const int id, RegistryItem<string>* item)
    {
        if(item != NULL)
        {
            Print("ID: ", id, ", 名稱: ", item.GetName(), ", 值: ", item.GetValue());
        }
    }
};
