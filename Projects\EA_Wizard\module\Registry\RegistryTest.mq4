//+------------------------------------------------------------------+
//|                                                 RegistryTest.mq4 |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RegistryExample.mqh"
#include "RegistryAdvancedExample.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
   // 測試字符串註冊器
   Print("===== 測試字符串註冊器 =====");
   RegistryExample::DemoStringRegistry();

   // 測試對象註冊器
   Print("===== 測試對象註冊器 =====");
   RegistryExample::DemoObjectRegistry();

   // 測試進階用法：整數鍵
   Print("===== 測試進階用法：整數鍵 =====");
   RegistryAdvancedExample::DemoIntKeyRegistry();

   // 測試進階用法：枚舉鍵
   Print("===== 測試進階用法：枚舉鍵 =====");
   RegistryAdvancedExample::DemoEnumKeyRegistry();

   Print("註冊器測試完成！");
}
//+------------------------------------------------------------------+
