#property strict

#include "OnTickStage/OnTickStage.mqh"

bool EAProcessor(){
    // // 獲取註冊器實例
    // StringRegistry<void*>* registry = new StringRegistry<void*>();
    Print("EA處理開始");

    // 創建並執行交易流水線處理器
    OnTickStageManager* processor = OnTickStageManager::GetInstance();
    bool result = processor.Execute();

    // 處理執行結果
    if(!result)
    {
        Print("EA處理失敗，交易流水線執行失敗");
        delete processor;
        return false;
    }

    Print("EA處理成功");
    return true;
}
