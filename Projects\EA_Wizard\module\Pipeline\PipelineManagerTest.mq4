//+------------------------------------------------------------------+
//|                                           PipelineManagerTest.mq4 |
//|                                                       EA_Wizard   |
//|                                                                   |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "PipelineManagerExample.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
   // 測試帶有 Registry 的 PipelineManager
   Print("===== 測試帶有 Registry 的 PipelineManager =====");
   PipelineManagerExample::Demo();
   
   Print("測試完成！");
}
//+------------------------------------------------------------------+
