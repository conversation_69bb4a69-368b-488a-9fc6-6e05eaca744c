#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"
#include "../../module/mql4-lib-master/Trade/Account.mqh"
#include "../../module/mql4-lib-master/Trade/FxSymbol.mqh"

// 資金管理類型
enum ENUM_MONEY_MANAGEMENT
{
    MONEY_MANAGEMENT_FIXED_LOTS,         // 固定手數
    MONEY_MANAGEMENT_FIXED_RISK_AMOUNT,  // 固定風險金額
    MONEY_MANAGEMENT_FIXED_RISK_PERCENT  // 固定風險百分比
};

// 止損類型
enum ENUM_STOP_LOSS_TYPE
{
    STOP_LOSS_FIXED_POINTS,              // 固定點數
    STOP_LOSS_ATR,                       // ATR倍數
    STOP_LOSS_SUPPORT_RESISTANCE         // 支撐阻力位
};

// 初始化風險管理階段
class InitRiskManagementStage : public OnInitPipeline
{
private:
    // 風險參數
    double m_max_risk_percent;
    double m_max_risk_amount;
    double m_max_position_size;

    // 資金管理參數
    ENUM_MONEY_MANAGEMENT m_money_management_type;
    double m_fixed_lots;
    double m_fixed_risk_amount;
    double m_fixed_risk_percent;

    // 止損參數
    ENUM_STOP_LOSS_TYPE m_stop_loss_type;
    int m_fixed_stop_loss_points;
    double m_atr_multiplier;
    int m_atr_period;

    // 追蹤止損參數
    double m_trailing_stop_activation;
    double m_trailing_stop_distance;

    // 其他風險控制參數
    double m_reward_risk_ratio;
    int m_max_trades;
    double m_max_daily_loss;
    double m_max_drawdown;

    // 計算基於風險百分比的倉位大小
    double CalculatePositionSizeByRiskPercent(double entryPrice, double stopLossPrice, string symbol)
    {
        // 計算止損點數
        double stopLossPoints = MathAbs(entryPrice - stopLossPrice) / FxSymbol::getPoint(symbol);

        // 獲取賬戶餘額
        double balance = Account::getBalance();

        // 計算風險金額
        double riskAmount = balance * m_fixed_risk_percent / 100.0;

        // 計算每點價值
        double tickValue = MarketInfo(symbol, MODE_TICKVALUE);
        double pointValue = tickValue / MarketInfo(symbol, MODE_POINT);

        // 計算倉位大小
        double positionSize = riskAmount / (stopLossPoints * pointValue);

        // 調整倉位大小
        positionSize = NormalizeDouble(positionSize, 2);
        positionSize = MathMin(positionSize, FxSymbol::getMaxLot(symbol));
        positionSize = MathMax(positionSize, FxSymbol::getMinLot(symbol));
        positionSize = FxSymbol::normalizeLots(symbol, positionSize);

        return positionSize;
    }

    // 檢查風險參數是否有效
    bool ValidateRiskParameters()
    {
        if(m_max_risk_percent <= 0 || m_max_risk_percent > 10)
        {
            Print("最大風險百分比無效: ", m_max_risk_percent, "%, 應在 0-10% 範圍內");
            return false;
        }

        if(m_reward_risk_ratio <= 0)
        {
            Print("獎勵風險比無效: ", m_reward_risk_ratio, ", 應大於 0");
            return false;
        }

        if(m_max_trades <= 0)
        {
            Print("最大交易數量無效: ", m_max_trades, ", 應大於 0");
            return false;
        }

        if(m_max_daily_loss <= 0 || m_max_daily_loss > 50)
        {
            Print("最大日虧損無效: ", m_max_daily_loss, "%, 應在 0-50% 範圍內");
            return false;
        }

        if(m_max_drawdown <= 0 || m_max_drawdown > 100)
        {
            Print("最大回撤無效: ", m_max_drawdown, "%, 應在 0-100% 範圍內");
            return false;
        }

        return true;
    }

public:
    // 建構函數
    InitRiskManagementStage(
        // 風險參數
        double max_risk_percent = 2.0,
        double max_risk_amount = 0.0,
        double max_position_size = 0.0,

        // 資金管理參數
        ENUM_MONEY_MANAGEMENT money_management_type = MONEY_MANAGEMENT_FIXED_RISK_PERCENT,
        double fixed_lots = 0.01,
        double fixed_risk_amount = 100.0,
        double fixed_risk_percent = 1.0,

        // 止損參數
        ENUM_STOP_LOSS_TYPE stop_loss_type = STOP_LOSS_FIXED_POINTS,
        int fixed_stop_loss_points = 50,
        double atr_multiplier = 2.0,
        int atr_period = 14,

        // 追蹤止損參數
        double trailing_stop_activation = 50.0,
        double trailing_stop_distance = 20.0,

        // 其他風險控制參數
        double reward_risk_ratio = 2.0,
        int max_trades = 5,
        double max_daily_loss = 5.0,
        double max_drawdown = 20.0
    )
    {
        // 風險參數
        m_max_risk_percent = max_risk_percent;
        m_max_risk_amount = max_risk_amount;
        m_max_position_size = max_position_size;

        // 資金管理參數
        m_money_management_type = money_management_type;
        m_fixed_lots = fixed_lots;
        m_fixed_risk_amount = fixed_risk_amount;
        m_fixed_risk_percent = fixed_risk_percent;

        // 止損參數
        m_stop_loss_type = stop_loss_type;
        m_fixed_stop_loss_points = fixed_stop_loss_points;
        m_atr_multiplier = atr_multiplier;
        m_atr_period = atr_period;

        // 追蹤止損參數
        m_trailing_stop_activation = trailing_stop_activation;
        m_trailing_stop_distance = trailing_stop_distance;

        // 其他風險控制參數
        m_reward_risk_ratio = reward_risk_ratio;
        m_max_trades = max_trades;
        m_max_daily_loss = max_daily_loss;
        m_max_drawdown = max_drawdown;
    }

    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 檢查風險參數是否有效
        if(!ValidateRiskParameters())
        {
            return INIT_PARAMETERS_INCORRECT;
        }

        // 檢查賬戶是否允許交易
        if(!Account::allowsTrade())
        {
            Print("賬戶不允許交易");
            return INIT_FAILED;
        }

        // 檢查賬戶是否允許EA交易
        if(!Account::allowsExpertTrade())
        {
            Print("賬戶不允許EA交易");
            return INIT_FAILED;
        }

        // 獲取賬戶信息
        double account_balance = Account::getBalance();
        double account_equity = Account::getEquity();
        double account_margin = Account::getMargin();
        double account_free_margin = Account::getFreeMargin();
        double account_margin_level = Account::getMarginLevel();

        // 計算風險金額
        double risk_amount = account_balance * m_fixed_risk_percent / 100.0;

        // 計算示例倉位大小
        string symbol = Symbol();
        double entry_price = SymbolInfoDouble(symbol, SYMBOL_ASK);
        double stop_loss_price = entry_price - m_fixed_stop_loss_points * FxSymbol::getPoint(symbol);
        double position_size = CalculatePositionSizeByRiskPercent(entry_price, stop_loss_price, symbol);

        // 將風險管理參數註冊到註冊器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();

        // 註冊風險參數
        registry.Register("MaxRiskPercent", "最大風險百分比", &m_max_risk_percent);
        registry.Register("MaxRiskAmount", "最大風險金額", &m_max_risk_amount);
        registry.Register("MaxPositionSize", "最大倉位大小", &m_max_position_size);

        // 註冊資金管理參數
        registry.Register("MoneyManagementType", "資金管理類型", &m_money_management_type);
        registry.Register("FixedLots", "固定手數", &m_fixed_lots);
        registry.Register("FixedRiskAmount", "固定風險金額", &m_fixed_risk_amount);
        registry.Register("FixedRiskPercent", "固定風險百分比", &m_fixed_risk_percent);

        // 註冊止損參數
        registry.Register("StopLossType", "止損類型", &m_stop_loss_type);
        registry.Register("FixedStopLossPoints", "固定止損點數", &m_fixed_stop_loss_points);
        registry.Register("ATRMultiplier", "ATR倍數", &m_atr_multiplier);
        registry.Register("ATRPeriod", "ATR週期", &m_atr_period);

        // 註冊追蹤止損參數
        registry.Register("TrailingStopActivation", "追蹤止損激活點數", &m_trailing_stop_activation);
        registry.Register("TrailingStopDistance", "追蹤止損距離", &m_trailing_stop_distance);

        // 註冊其他風險控制參數
        registry.Register("RewardRiskRatio", "獎勵風險比", &m_reward_risk_ratio);
        registry.Register("MaxTrades", "最大交易數量", &m_max_trades);
        registry.Register("MaxDailyLoss", "最大日虧損百分比", &m_max_daily_loss);
        registry.Register("MaxDrawdown", "最大回撤百分比", &m_max_drawdown);

        // 註冊賬戶信息
        registry.Register("AccountBalance", "賬戶餘額", &account_balance);
        registry.Register("AccountEquity", "賬戶淨值", &account_equity);
        registry.Register("AccountMargin", "賬戶保證金", &account_margin);
        registry.Register("AccountFreeMargin", "賬戶可用保證金", &account_free_margin);
        registry.Register("AccountMarginLevel", "賬戶保證金水平", &account_margin_level);

        // 註冊計算結果
        registry.Register("RiskAmount", "風險金額", &risk_amount);
        registry.Register("PositionSize", "倉位大小", &position_size);

        Print("風險管理初始化成功 - 資金管理類型: ", EnumToString(m_money_management_type),
              ", 風險百分比: ", m_fixed_risk_percent, "%",
              ", 止損類型: ", EnumToString(m_stop_loss_type),
              ", 止損點數: ", m_fixed_stop_loss_points,
              ", 計算倉位大小: ", position_size);

        return INIT_SUCCEEDED;
    }
};
