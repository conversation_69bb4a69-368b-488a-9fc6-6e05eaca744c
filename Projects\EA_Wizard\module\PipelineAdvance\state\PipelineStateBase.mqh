//+------------------------------------------------------------------+
//|                                           PipelineStateBase.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../interface/IPipelineState.mqh"

//+------------------------------------------------------------------+
//| 流水線狀態基類 - 實現 IPipelineState 介面的基本功能              |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStateBase : public IPipelineState<PipelineObj>
{
protected:
    ENUM_PIPELINE_STATE m_status;      // 狀態類型
    string m_description;               // 狀態描述
    
public:
    // 構造函數
    PipelineStateBase(ENUM_PIPELINE_STATE status, string description)
    : m_status(status), m_description(description) {}
    
    // 析構函數
    ~PipelineStateBase() {}
    
    // 獲取狀態類型
    ENUM_PIPELINE_STATE GetState() override { return m_status; }
    
    // 獲取狀態描述
    string GetStateDescription() override { return m_description; }
    
    // 執行流水線（由子類實現）
    virtual void Execute(PipelineObj pipeline) override = 0;
    
    // 重置流水線狀態（由子類實現）
    virtual void Restore(PipelineObj pipeline) override = 0;
};
