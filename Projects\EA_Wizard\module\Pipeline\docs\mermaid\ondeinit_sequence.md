```mermaid
sequenceDiagram
    participant EA as EA
    participant Processor as DeinitPipelineProcessor
    participant Stage1 as CleanupStage
    participant Stage2 as LoggingStage
    participant Registry as Registry

    EA->>Processor: 創建處理器
    Processor->>Stage1: 添加 CleanupStage
    Processor->>Stage2: 添加 LoggingStage
    EA->>Processor: Execute(reason)
    Processor->>Stage1: Execute(reason)
    Stage1->>Registry: 清理註冊器
    Registry-->>Stage1: 返回清理結果
    Stage1-->>Processor: 返回 true
    Processor->>Stage2: Execute(reason)
    Stage2-->>Processor: 返回 true
    Processor-->>EA: 返回 true
    EA->>Processor: 釋放資源
```
