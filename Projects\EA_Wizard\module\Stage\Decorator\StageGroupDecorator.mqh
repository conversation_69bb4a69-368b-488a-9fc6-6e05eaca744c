#property strict

#include "../../Util/MessageHandler.mqh"
#include "../../Util/EAFileLog.mqh"
#include "../interface/IStageGroup.mqh"
#include "../../mql4-lib-master/Collection/HashMap.mqh"
#include "../../Util/Util.mqh"

enum ENUM_STAGE_GROUP_DECORATOR
{
    STAGE_GROUP_DECORATOR_DEFAULT,
    STAGE_GROUP_DECORATOR_MSG_HANDLER,
    STAGE_GROUP_DECORATOR_FILE_LOG
};

template <typename Output, typename Input>
class StageGroupDecorator : public IStageGroup<Output, Input> {
private:
    IStageGroup<Output, Input>* m_stageGroup;

public:
    StageGroupDecorator(IStageGroup<Output, Input>* stageGroup)
    : m_stageGroup(stageGroup) {
    }

    // 執行階段群組
    virtual Output Execute(Input in) override {
        return m_stageGroup.Execute(in);
    }
    virtual bool IsError() override { return m_stageGroup.IsError(); }
    virtual string LastError() override { return m_stageGroup.LastError(); }

    virtual bool HasPipeline(IPipeline<Output, Input>* pipeline) const override { return m_stageGroup.HasPipeline(pipeline); }
    virtual bool AddPipeline(IPipeline<Output, Input>* pipeline) override { return m_stageGroup.AddPipeline(pipeline); }
    virtual bool RemovePipeline(IPipeline<Output, Input>* pipeline) override { return m_stageGroup.RemovePipeline(pipeline); }
    virtual void ClearPipelines() override { m_stageGroup.ClearPipelines(); }

    virtual string GetName() const override { return m_stageGroup.GetName(); }
    virtual int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const override { return m_stageGroup.GetPipelines(pipelines); }

    virtual bool IsRegistered() const override { return m_stageGroup.IsRegistered(); }
    virtual void SetRegistered(bool registered) override { m_stageGroup.SetRegistered(registered); }

};

template <typename Output, typename Input>
class MsgHandlerStageGroup : public StageGroupDecorator<Output, Input> {
private:
    MessageHandler* m_messageHandler;

public:
    MsgHandlerStageGroup(IStageGroup<Output, Input>* stageGroup, MessageHandler* messageHandler = NULL)
    : StageGroupDecorator(stageGroup), m_messageHandler(messageHandler? messageHandler : MessageHandler::GetInstance()) {
    }

    virtual bool HasPipeline(IPipeline<Output, Input>* pipeline) const override {
        if(StageGroupDecorator<Output, Input>::HasPipeline(pipeline)){
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段已存在！");
            return true;
        }

        m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段不存在！");
        return false;
    }
    virtual bool AddPipeline(IPipeline<Output, Input>* pipeline) override {
        if(StageGroupDecorator<Output, Input>::AddPipeline(pipeline)){
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段新增成功！");
            return true;
        }

        m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段新增失敗！");
        return false;
    }
    virtual bool RemovePipeline(IPipeline<Output, Input>* pipeline) override {
        if(StageGroupDecorator<Output, Input>::RemovePipeline(pipeline)){
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段移除成功！");
            return true;
        }

        m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段移除失敗！");
        return false;
    }
    virtual void ClearPipelines() override {
        StageGroupDecorator<Output, Input>::ClearPipelines();
        m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段群組已清空！");
    }

    virtual bool IsRegistered() const override {
        if(StageGroupDecorator<Output, Input>::IsRegistered()){
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段群組已註冊！");
            return true;
        }

        m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段群組未註冊！");
        return false;
    }
    virtual void SetRegistered(bool registered) override {
        StageGroupDecorator<Output, Input>::SetRegistered(registered);
        if(registered){
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段群組已註冊！");
        } else {
            m_messageHandler.AddMessage(MSG_TYPE_SYSTEM, StageGroupDecorator<Output, Input>::GetName() + "流水線階段群組已取消註冊！");
        }
    }

};

template <typename Output, typename Input>
class EAFileLogStageGroup : public StageGroupDecorator<Output, Input> {
private:
    EAFileLog* m_fileLog;

public:
    EAFileLogStageGroup(IStageGroup<Output, Input>* stageGroup, EAFileLog* fileLog = NULL)
    : StageGroupDecorator(stageGroup), m_fileLog(fileLog? fileLog : EAFileLog::GetInstance()) {
    }

    virtual Output Execute(Input in) override {
        m_fileLog.Debug(StringFormat("開始執行階段群組: %s", StageGroupDecorator<Output, Input>::GetName()));
        Output result = StageGroupDecorator<Output, Input>::Execute(in);
        m_fileLog.Debug(StringFormat("完成執行階段群組: %s", StageGroupDecorator<Output, Input>::GetName()));
        return result;
    }

    virtual bool HasPipeline(IPipeline<Output, Input>* pipeline) const override {
        bool result = StageGroupDecorator<Output, Input>::HasPipeline(pipeline);
        m_fileLog.Debug(StringFormat("檢查階段群組 %s 是否包含流水線: %s",
                                    StageGroupDecorator<Output, Input>::GetName(),
                                    result ? "是" : "否"));
        return result;
    }

    virtual bool AddPipeline(IPipeline<Output, Input>* pipeline) override {
        bool result = StageGroupDecorator<Output, Input>::AddPipeline(pipeline);
        m_fileLog.Debug(StringFormat("階段群組 %s 新增流水線: %s",
                                    StageGroupDecorator<Output, Input>::GetName(),
                                    result ? "成功" : "失敗"));
        return result;
    }

    virtual bool RemovePipeline(IPipeline<Output, Input>* pipeline) override {
        bool result = StageGroupDecorator<Output, Input>::RemovePipeline(pipeline);
        m_fileLog.Debug(StringFormat("階段群組 %s 移除流水線: %s",
                                    StageGroupDecorator<Output, Input>::GetName(),
                                    result ? "成功" : "失敗"));
        return result;
    }

    virtual void ClearPipelines() override {
        StageGroupDecorator<Output, Input>::ClearPipelines();
        m_fileLog.Debug(StringFormat("階段群組 %s 已清空所有流水線", StageGroupDecorator<Output, Input>::GetName()));
    }

    virtual bool IsRegistered() const override {
        bool result = StageGroupDecorator<Output, Input>::IsRegistered();
        m_fileLog.Debug(StringFormat("階段群組 %s 註冊狀態: %s",
                                    StageGroupDecorator<Output, Input>::GetName(),
                                    result ? "已註冊" : "未註冊"));
        return result;
    }

    virtual void SetRegistered(bool registered) override {
        StageGroupDecorator<Output, Input>::SetRegistered(registered);
        m_fileLog.Debug(StringFormat("階段群組 %s 設置註冊狀態: %s",
                                    StageGroupDecorator<Output, Input>::GetName(),
                                    registered ? "已註冊" : "未註冊"));
                                    //DebugBreak();
    }
};

template <typename Output, typename Input>
class StageGroupDecoratorResult {
private:
    StageGroupDecorator<Output, Input>* m_result;
    bool m_isAvailable;
public:
    StageGroupDecoratorResult(StageGroupDecorator<Output, Input>* result, bool isAvailable)
    : m_result(result), m_isAvailable(isAvailable) {
    }

    ~StageGroupDecoratorResult() {
        if(m_result != NULL) {
            delete m_result;
            m_result = NULL;
        }
    }

    bool IsAvailable() const { return m_isAvailable; }
    StageGroupDecorator<Output, Input>* GetDecorator() const { return m_result; }
};

typedef StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>* (*InitStageGroupDecoratorResultFunc)(IStageGroup<ENUM_INIT_RETCODE, void*>* stageGroup, void* object);
typedef StageGroupDecoratorResult<bool, void*>* (*TickStageGroupDecoratorResultFunc)(IStageGroup<bool, void*>* stageGroup, void* object);
typedef StageGroupDecoratorResult<bool, int>* (*DeinitStageGroupDecoratorResultFunc)(IStageGroup<bool, int>* stageGroup, void* object);

class StageGroupDecoratorFactory {
public:
    template <typename Output, typename Input>
    static StageGroupDecoratorResult<Output, Input>* CreateMsgHandlerStageGroupResult(IStageGroup<Output, Input>* stageGroup, void* object = NULL) {
        MessageHandler* obj = dynamic_cast<MessageHandler*>(object);
        if(obj == NULL && object != NULL)
            return new StageGroupDecoratorResult<Output, Input>(NULL, false);

        StageGroupDecorator<Output, Input>* decorator = CreateMsgHandlerStageGroup(stageGroup, obj? obj : MessageHandler::GetInstance());
        return new StageGroupDecoratorResult<Output, Input>(decorator, true);
    }
    template <typename Output, typename Input>
    static StageGroupDecorator<Output, Input>* CreateMsgHandlerStageGroup(IStageGroup<Output, Input>* stageGroup, MessageHandler* messageHandler = NULL) {
        return new MsgHandlerStageGroup<Output, Input>(stageGroup, messageHandler? messageHandler : MessageHandler::GetInstance());
    }

    template <typename Output, typename Input>
    static StageGroupDecoratorResult<Output, Input>* CreateEAFileLogStageGroupResult(IStageGroup<Output, Input>* stageGroup, void* object = NULL) {
        EAFileLog* obj = dynamic_cast<EAFileLog*>(object);
        if(obj == NULL && object != NULL)
            return new StageGroupDecoratorResult<Output, Input>(NULL, false);

        StageGroupDecorator<Output, Input>* decorator = CreateEAFileLogStageGroup(stageGroup, obj? obj : EAFileLog::GetInstance());
        return new StageGroupDecoratorResult<Output, Input>(decorator, true);
    }
    template <typename Output, typename Input>
    static StageGroupDecorator<Output, Input>* CreateEAFileLogStageGroup(IStageGroup<Output, Input>* stageGroup, EAFileLog* fileLog = NULL) {
        return new EAFileLogStageGroup<Output, Input>(stageGroup, fileLog? fileLog : EAFileLog::GetInstance());
    }
};

class InitStageGroupDecoratorResultBuilder{
private:
    InitStageGroupDecoratorResultFunc m_decoratorResultFunc[];
    IStageGroup<ENUM_INIT_RETCODE, void*>* m_stageGroup;
    StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>* m_result;

public:
    InitStageGroupDecoratorResultBuilder() {
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void Reset() {
        ArrayFree(m_decoratorResultFunc);
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void SetStageGroup(IStageGroup<ENUM_INIT_RETCODE, void*>* stageGroup) {
        m_stageGroup = stageGroup;
    }

    void SetDecoratorResultFunc(InitStageGroupDecoratorResultFunc func) {
        ArrayResize(m_decoratorResultFunc, ArraySize(m_decoratorResultFunc) + 1);
        m_decoratorResultFunc[ArraySize(m_decoratorResultFunc) - 1] = func;
    }

    StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>* Build() {
        if(m_stageGroup == NULL) {
            m_result = new StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>(NULL, false);
        }
        else if(m_result != NULL) {
            m_result = m_result;
        }
        else if(ArraySize(m_decoratorResultFunc) <= 0){
            m_result = new StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>(new StageGroupDecorator<ENUM_INIT_RETCODE, void*>(m_stageGroup), true);
        }
        else{
            IStageGroup<ENUM_INIT_RETCODE, void*>* stageGroup = m_stageGroup;
            StageGroupDecoratorResult<ENUM_INIT_RETCODE, void*>* result = NULL;
            for(int i = 0; i < ArraySize(m_decoratorResultFunc); i++) {
                result = m_decoratorResultFunc[i](stageGroup, NULL);
                stageGroup = result.GetDecorator();
            }
            m_result = result;
        }
        return m_result;
    }

};

class InitStageGroupDecoratorReusltMapper {
private:
    static InitStageGroupDecoratorReusltMapper* s_instance;
    Map<int, InitStageGroupDecoratorResultFunc>* m_map;

    InitStageGroupDecoratorReusltMapper()
    {
        m_map = new HashMap<int, InitStageGroupDecoratorResultFunc>();
        InitializeMap();
    }

    ~InitStageGroupDecoratorReusltMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)STAGE_GROUP_DECORATOR_MSG_HANDLER, StageGroupDecoratorFactory::CreateMsgHandlerStageGroupResult<ENUM_INIT_RETCODE, void*>);
        m_map.set((int)STAGE_GROUP_DECORATOR_FILE_LOG, StageGroupDecoratorFactory::CreateEAFileLogStageGroupResult<ENUM_INIT_RETCODE, void*>);
    }

public:
    static InitStageGroupDecoratorReusltMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new InitStageGroupDecoratorReusltMapper();
        }
        return s_instance;
    }

    InitStageGroupDecoratorResultFunc GetDecoratorResultFunc(ENUM_STAGE_GROUP_DECORATOR decorator)
    {
        return m_map.get((int)decorator, NULL);
    }

};

InitStageGroupDecoratorReusltMapper* InitStageGroupDecoratorReusltMapper::s_instance = NULL;

class TickStageGroupDecoratorResultBuilder{
private:
    TickStageGroupDecoratorResultFunc m_decoratorResultFunc[];
    IStageGroup<bool, void*>* m_stageGroup;
    StageGroupDecoratorResult<bool, void*>* m_result;

public:
    TickStageGroupDecoratorResultBuilder() {
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void Reset() {
        ArrayFree(m_decoratorResultFunc);
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void SetStageGroup(IStageGroup<bool, void*>* stageGroup) {
        m_stageGroup = stageGroup;
    }

    void SetDecoratorResultFunc(TickStageGroupDecoratorResultFunc func) {
        ArrayResize(m_decoratorResultFunc, ArraySize(m_decoratorResultFunc) + 1);
        m_decoratorResultFunc[ArraySize(m_decoratorResultFunc) - 1] = func;
    }

    StageGroupDecoratorResult<bool, void*>* Build() {
        if(m_stageGroup == NULL) {
            m_result = new StageGroupDecoratorResult<bool, void*>(NULL, false);
        }
        else if(m_result != NULL) {
            m_result = m_result;
        }
        else if(ArraySize(m_decoratorResultFunc) <= 0){
            m_result = new StageGroupDecoratorResult<bool, void*>(new StageGroupDecorator<bool, void*>(m_stageGroup), true);
        }
        else{
            IStageGroup<bool, void*>* stageGroup = m_stageGroup;
            StageGroupDecoratorResult<bool, void*>* result = NULL;
            for(int i = 0; i < ArraySize(m_decoratorResultFunc); i++) {
                result = m_decoratorResultFunc[i](stageGroup, NULL);
                stageGroup = result.GetDecorator();
            }
            m_result = result;
        }
        return m_result;
    }
};

class TickStageGroupDecoratorReusltMapper {
private:
    static TickStageGroupDecoratorReusltMapper* s_instance;
    Map<int, TickStageGroupDecoratorResultFunc>* m_map;

    TickStageGroupDecoratorReusltMapper()
    {
        m_map = new HashMap<int, TickStageGroupDecoratorResultFunc>();
        InitializeMap();
    }

    ~TickStageGroupDecoratorReusltMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)STAGE_GROUP_DECORATOR_MSG_HANDLER, StageGroupDecoratorFactory::CreateMsgHandlerStageGroupResult<bool, void*>);
        m_map.set((int)STAGE_GROUP_DECORATOR_FILE_LOG, StageGroupDecoratorFactory::CreateEAFileLogStageGroupResult<bool, void*>);
    }

public:
    static TickStageGroupDecoratorReusltMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new TickStageGroupDecoratorReusltMapper();
        }
        return s_instance;
    }

    TickStageGroupDecoratorResultFunc GetDecoratorResultFunc(ENUM_STAGE_GROUP_DECORATOR decorator)
    {
        return m_map.get((int)decorator, NULL);
    }
};

TickStageGroupDecoratorReusltMapper* TickStageGroupDecoratorReusltMapper::s_instance = NULL;

class DeinitStageGroupDecoratorResultBuilder{
private:
    DeinitStageGroupDecoratorResultFunc m_decoratorResultFunc[];
    IStageGroup<bool, int>* m_stageGroup;
    StageGroupDecoratorResult<bool, int>* m_result;

public:
    DeinitStageGroupDecoratorResultBuilder() {
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void Reset() {
        ArrayFree(m_decoratorResultFunc);
        m_stageGroup = NULL;
        m_result = NULL;
    }

    void SetStageGroup(IStageGroup<bool, int>* stageGroup) {
        m_stageGroup = stageGroup;
    }

    void SetDecoratorResultFunc(DeinitStageGroupDecoratorResultFunc func) {
        ArrayResize(m_decoratorResultFunc, ArraySize(m_decoratorResultFunc) + 1);
        m_decoratorResultFunc[ArraySize(m_decoratorResultFunc) - 1] = func;
    }

    StageGroupDecoratorResult<bool, int>* Build() {
        if(m_stageGroup == NULL) {
            m_result = new StageGroupDecoratorResult<bool, int>(NULL, false);
        }
        else if(m_result != NULL) {
            m_result = m_result;
        }
        else if(ArraySize(m_decoratorResultFunc) <= 0){
            m_result = new StageGroupDecoratorResult<bool, int>(new StageGroupDecorator<bool, int>(m_stageGroup), true);
        }
        else{
            IStageGroup<bool, int>* stageGroup = m_stageGroup;
            StageGroupDecoratorResult<bool, int>* result = NULL;
            for(int i = 0; i < ArraySize(m_decoratorResultFunc); i++) {
                result = m_decoratorResultFunc[i](stageGroup, NULL);
                stageGroup = result.GetDecorator();
            }
            m_result = result;
        }
        return m_result;
    }
};

class DeinitStageGroupDecoratorReusltMapper {
private:
    static DeinitStageGroupDecoratorReusltMapper* s_instance;
    Map<int, DeinitStageGroupDecoratorResultFunc>* m_map;

    DeinitStageGroupDecoratorReusltMapper()
    {
        m_map = new HashMap<int, DeinitStageGroupDecoratorResultFunc>();
        InitializeMap();
    }

    ~DeinitStageGroupDecoratorReusltMapper()
    {
        delete m_map;
    }

    void InitializeMap()
    {
        m_map.set((int)STAGE_GROUP_DECORATOR_MSG_HANDLER, StageGroupDecoratorFactory::CreateMsgHandlerStageGroupResult<bool, int>);
        m_map.set((int)STAGE_GROUP_DECORATOR_FILE_LOG, StageGroupDecoratorFactory::CreateEAFileLogStageGroupResult<bool, int>);
    }

public:
    static DeinitStageGroupDecoratorReusltMapper* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new DeinitStageGroupDecoratorReusltMapper();
        }
        return s_instance;
    }

    DeinitStageGroupDecoratorResultFunc GetDecoratorResultFunc(ENUM_STAGE_GROUP_DECORATOR decorator)
    {
        return m_map.get((int)decorator, NULL);
    }
};

DeinitStageGroupDecoratorReusltMapper* DeinitStageGroupDecoratorReusltMapper::s_instance = NULL;
