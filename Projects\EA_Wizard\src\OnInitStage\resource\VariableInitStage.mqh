#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"
#include "../../module/mql4-lib-master/Trade/OrderManager.mqh"

// 變數初始化階段
class VariableInitStage : public OnInitPipeline
{
private:
    string m_symbol;
    int m_digits;
    double m_point;
    double m_tick_size;
    double m_tick_value;
    double m_min_lot;
    double m_max_lot;
    double m_lot_step;
    
public:
    // 建構函數
    VariableInitStage(string symbol = NULL)
        : m_symbol(symbol == NULL ? Symbol() : symbol)
    {
    }
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 獲取交易品種信息
        m_digits = (int)MarketInfo(m_symbol, MODE_DIGITS);
        m_point = MarketInfo(m_symbol, MODE_POINT);
        m_tick_size = MarketInfo(m_symbol, MODE_TICKSIZE);
        m_tick_value = MarketInfo(m_symbol, MODE_TICKVALUE);
        m_min_lot = MarketInfo(m_symbol, MODE_MINLOT);
        m_max_lot = MarketInfo(m_symbol, MODE_MAXLOT);
        m_lot_step = MarketInfo(m_symbol, MODE_LOTSTEP);
        
        // 檢查交易品種信息是否有效
        if(m_digits <= 0 || m_point <= 0 || m_tick_size <= 0 || m_tick_value <= 0 || 
           m_min_lot <= 0 || m_max_lot <= 0 || m_lot_step <= 0)
        {
            Print("交易品種信息無效: ", m_symbol);
            return INIT_FAILED;
        }
        
        // 初始化訂單管理器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        int magic_number = 0;
        int slippage = 0;
        
        // 從註冊器獲取參數
        void* magic_ptr = NULL;
        if(registry.Get("MagicNumber", magic_ptr))
        {
            magic_number = *(int*)magic_ptr;
        }
        else
        {
            magic_number = 12345; // 預設值
        }
        
        void* slippage_ptr = NULL;
        if(registry.Get("Slippage", slippage_ptr))
        {
            slippage = *(int*)slippage_ptr;
        }
        else
        {
            slippage = 3; // 預設值
        }
        
        // 創建訂單管理器
        OrderManager* order_manager = new OrderManager(m_symbol);
        order_manager.setMagic(magic_number);
        order_manager.setSlippage(slippage);
        order_manager.setRetry(10);
        order_manager.setBuyColor(clrBlue);
        order_manager.setSellColor(clrRed);
        order_manager.setCloseColor(clrWhite);
        
        // 將訂單管理器註冊到註冊器
        registry.Register("OrderManager", "訂單管理器", order_manager);
        
        // 將交易品種信息註冊到註冊器
        registry.Register("Symbol", "交易品種", m_symbol);
        registry.Register("Digits", "小數位數", &m_digits);
        registry.Register("Point", "點值", &m_point);
        registry.Register("TickSize", "最小價格變動", &m_tick_size);
        registry.Register("TickValue", "最小價格變動的價值", &m_tick_value);
        registry.Register("MinLot", "最小交易量", &m_min_lot);
        registry.Register("MaxLot", "最大交易量", &m_max_lot);
        registry.Register("LotStep", "交易量步長", &m_lot_step);
        
        Print("變數初始化成功 - 交易品種: ", m_symbol, 
              ", 小數位數: ", m_digits, 
              ", 點值: ", m_point, 
              ", 最小交易量: ", m_min_lot, 
              ", 最大交易量: ", m_max_lot);
        
        return INIT_SUCCEEDED;
    }
};
