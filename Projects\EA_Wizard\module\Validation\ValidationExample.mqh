#property strict

#include "ValidationGroup.mqh"
#include "Validators/ValidatorCollection.mqh"
#include "ValidationPipeline.mqh"
#include "../Pipeline/PipelineManager.mqh"

//+------------------------------------------------------------------+
//| 驗證模組使用示例                                                    |
//+------------------------------------------------------------------+
class CValidationExample
{
public:
    // 基本驗證示例
    static void DemoBasicValidation()
    {
        Print("===== 基本驗證示例 =====");
        
        // 創建驗證器
        CRequiredValidator* requiredValidator = new CRequiredValidator("", "Symbol");
        CStringValidator* stringValidator = new CStringValidator("EURUSD", 3, 10, "Symbol");
        CRangeValidator<double>* rangeValidator = new CRangeValidator<double>(1.5, 0.1, 1.0, "Lot");
        
        // 執行驗證
        CValidationResult* result1 = requiredValidator.Validate();
        CValidationResult* result2 = stringValidator.Validate();
        CValidationResult* result3 = rangeValidator.Validate();
        
        // 輸出結果
        Print("必填驗證結果: ", result1.ToString());
        Print("字符串驗證結果: ", result2.ToString());
        Print("範圍驗證結果: ", result3.ToString());
        
        // 清理資源
        delete result1;
        delete result2;
        delete result3;
        delete requiredValidator;
        delete stringValidator;
        delete rangeValidator;
    }
    
    // 驗證器組合示例
    static void DemoValidatorComposite()
    {
        Print("===== 驗證器組合示例 =====");
        
        // 創建驗證器組合
        CValidatorComposite* composite = new CValidatorComposite();
        
        // 添加驗證器
        composite.Add(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
        composite.Add(CValidatorFactory::CreateStringValidator("EURUSD", 3, 10, "Symbol"));
        composite.Add(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));
        
        // 執行驗證
        CValidationResult* result = composite.Validate();
        
        // 輸出結果
        Print("組合驗證結果: ", result.ToString());
        
        // 清理資源
        delete result;
        delete composite;
    }
    
    // 驗證群組示例
    static void DemoValidationGroup()
    {
        Print("===== 驗證群組示例 =====");
        
        // 創建驗證群組
        CValidationGroup* group = new CValidationGroup("TradeValidation");
        
        // 添加驗證器
        group.AddValidator(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
        group.AddValidator(CValidatorFactory::CreateStringValidator("EURUSD", 3, 10, "Symbol"));
        group.AddValidator(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));
        
        // 設置是否在第一個錯誤時停止
        group.SetStopOnFirstError(true);
        
        // 執行驗證
        CValidationResult* result = group.ValidateAll();
        
        // 輸出結果
        Print("群組驗證結果: ", result.ToString());
        Print("最後錯誤: ", group.GetLastError());
        
        // 清理資源
        delete result;
        delete group;
    }
    
    // 驗證流水線示例
    static void DemoValidationPipeline()
    {
        Print("===== 驗證流水線示例 =====");
        
        // 創建流水線管理器
        PipelineManager<bool, void*>* manager = new PipelineManager<bool, void*>();
        
        // 創建驗證流水線階段
        CValidationPipeline* validationPipeline = new CValidationPipeline("TradeValidation");
        
        // 添加驗證器
        validationPipeline.AddValidator(CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol"));
        validationPipeline.AddValidator(CValidatorFactory::CreateStringValidator("EURUSD", 3, 10, "Symbol"));
        validationPipeline.AddValidator(CValidatorFactory::CreateRangeValidator<double>(0.5, 0.1, 1.0, "Lot"));
        
        // 設置驗證失敗時是否繼續流水線
        validationPipeline.SetContinueOnError(false);
        
        // 添加階段到流水線
        manager.AddPipeline(validationPipeline);
        
        // 執行流水線
        bool result = manager.Execute();
        
        // 輸出結果
        Print("流水線執行結果: ", (result ? "成功" : "失敗"));
        
        // 清理資源
        delete manager; // 這將自動刪除所有流水線階段
    }
    
    // 執行所有示例
    static void RunAllExamples()
    {
        DemoBasicValidation();
        DemoValidatorComposite();
        DemoValidationGroup();
        DemoValidationPipeline();
        
        Print("所有示例執行完成！");
    }
};
