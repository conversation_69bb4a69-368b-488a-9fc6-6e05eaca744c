#property strict

//+------------------------------------------------------------------+
//|                     驗證器常量定義                                  |
//+------------------------------------------------------------------+

// 驗證器識別常量
#define VALIDATOR_ID_REQUIRED     "required"    // 必填驗證器ID
#define VALIDATOR_ID_RANGE        "range"       // 範圍驗證器ID
#define VALIDATOR_ID_STRING       "string"      // 字串驗證器ID
#define VALIDATOR_ID_UNIQUE       "unique"      // 唯一性驗證器ID
#define VALIDATOR_ID_DATE         "date"        // 日期驗證器ID
#define VALIDATOR_ID_EMAIL        "email"       // 電子郵件驗證器ID
#define VALIDATOR_ID_NUMERIC      "numeric"     // 數值驗證器ID
#define VALIDATOR_ID_REGEX        "regex"       // 正則表達式驗證器ID
#define VALIDATOR_ID_LIST         "list"        // 列表驗證器ID
#define VALIDATOR_ID_CUSTOM       "custom"      // 自定義驗證器ID

// 驗證器類型常量
#define VALIDATOR_TYPE_REQUIRED    1       // 必填驗證
#define VALIDATOR_TYPE_RANGE       2       // 範圍驗證
#define VALIDATOR_TYPE_STRING      3       // 字串驗證
#define VALIDATOR_TYPE_UNIQUE      4       // 唯一性驗證
#define VALIDATOR_TYPE_DATE        5       // 日期驗證
#define VALIDATOR_TYPE_EMAIL       6       // 電子郵件驗證
#define VALIDATOR_TYPE_NUMERIC     7       // 數值驗證
#define VALIDATOR_TYPE_REGEX       8       // 正則表達式驗證
#define VALIDATOR_TYPE_LIST        9       // 列表驗證
#define VALIDATOR_TYPE_CUSTOM      10      // 自定義驗證

// 驗證錯誤訊息模板
#define VALIDATOR_MSG_RANGE_ERROR    "值必須在 %s 和 %s 之間"
#define VALIDATOR_MSG_STRING_LENGTH  "字串長度必須在 %d 和 %d 之間"
#define VALIDATOR_MSG_REQUIRED       "欄位 %s 為必填項"
#define VALIDATOR_MSG_UNIQUE         "%s 必須是唯一的"
#define VALIDATOR_MSG_INVALID_DATE   "無效的日期格式或順序"
#define VALIDATOR_MSG_INVALID_EMAIL  "無效的電子郵件格式"
#define VALIDATOR_MSG_INVALID_NUMBER "必須是有效的數字"
#define VALIDATOR_MSG_REGEX_FAILED   "格式不符合要求"
#define VALIDATOR_MSG_LIST_SIZE      "列表大小必須在 %d 和 %d 之間"
#define VALIDATOR_MSG_LIST_ITEM      "列表項 #%d 驗證失敗: %s"
#define VALIDATOR_MSG_CUSTOM_FAILED  "自定義驗證失敗: %s"

// 驗證結果常量
#define VALIDATOR_RESULT_VALID     true    // 驗證通過
#define VALIDATOR_RESULT_INVALID   false   // 驗證失敗
#define VALIDATOR_SOURCE_DEFAULT   "system" // 預設驗證來源

// 驗證器狀態常量
#define VALIDATOR_STATUS_ENABLED   true    // 驗證器啟用
#define VALIDATOR_STATUS_DISABLED  false   // 驗證器停用

// 驗證器限制常量
#define VALIDATOR_MAX_CHAIN        10      // 最大鏈式驗證數量
#define VALIDATOR_MAX_ERROR_LEN    255     // 最大錯誤訊息長度
#define VALIDATOR_MAX_VALIDATORS   50      // 最大驗證器數量
#define VALIDATOR_MAX_FIELDS      1000     // 最大欄位數量

// 字串驗證限制
#define VALIDATOR_MIN_STRING_LEN   0       // 最小字串長度
#define VALIDATOR_MAX_STRING_LEN   255     // 最大字串長度

// 數值驗證限制
#define VALIDATOR_MIN_DOUBLE      -1e+308  // 最小浮點數值
#define VALIDATOR_MAX_DOUBLE       1e+308  // 最大浮點數值
#define VALIDATOR_MIN_INT         -2147483648  // 最小整數值
#define VALIDATOR_MAX_INT          2147483647  // 最大整數值

// 日期時間驗證常量
#define VALIDATOR_MIN_YEAR        1970     // 最小年份
#define VALIDATOR_MAX_YEAR        2100     // 最大年份
#define VALIDATOR_DATE_FORMAT     "yyyy.mm.dd"  // 日期格式
#define VALIDATOR_TIME_FORMAT     "HH:MM:SS"    // 時間格式

// 驗證器錯誤代碼
#define VALIDATOR_ERROR_NONE      0        // 無錯誤
#define VALIDATOR_ERROR_REQUIRED  1        // 必填錯誤
#define VALIDATOR_ERROR_RANGE     2        // 範圍錯誤
#define VALIDATOR_ERROR_FORMAT    3        // 格式錯誤
#define VALIDATOR_ERROR_CUSTOM    4        // 自訂錯誤
#define VALIDATOR_ERROR_SYSTEM    5        // 系統錯誤

// 驗證器訊息級別
#define VALIDATOR_MSG_INFO        0        // 信息
#define VALIDATOR_MSG_WARNING     1        // 警告
#define VALIDATOR_MSG_ERROR       2        // 錯誤
#define VALIDATOR_MSG_CRITICAL    3        // 嚴重錯誤

// 驗證器名稱常量
#define VALIDATOR_NAME_RANGE         "RangeValidator"
#define VALIDATOR_NAME_STRING        "StringValidator"
#define VALIDATOR_NAME_REQUIRED      "RequiredValidator"
#define VALIDATOR_NAME_UNIQUE        "UniquenessValidator"
#define VALIDATOR_NAME_DATE          "DateValidator"
#define VALIDATOR_NAME_EMAIL         "EmailValidator"
#define VALIDATOR_NAME_NUMERIC       "NumericValidator"
#define VALIDATOR_NAME_REGEX         "RegexValidator"
#define VALIDATOR_NAME_LIST          "ListValidator"
#define VALIDATOR_NAME_CUSTOM        "CustomValidator"
