#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// OnTick 開始階段
class OnTickStartStage : public OnTickPipeline
{
public:
    bool Execute(void* in = NULL) override
    {
        // 檢查交易是否允許
        if(!IsTradeAllowed())
        {
            Print("交易不允許");
            return false;
        }
        
        // 檢查是否連接到交易伺服器
        if(!IsConnected())
        {
            Print("未連接到交易伺服器");
            return false;
        }
        
        Print("OnTick 開始執行");
        return true;
    }
};
