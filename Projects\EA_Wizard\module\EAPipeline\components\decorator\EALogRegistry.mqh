#property strict

#include "../EARegistryBase.mqh"
#include "../EAFileLog.mqh"

#define EA_LOG_REGISTRY_NAME "EALogRegistry"
#define EA_LOG_REGISTRY_TYPE "EALogRegistry"

//+------------------------------------------------------------------+
//| EALogRegistry 類 - 日誌裝飾者模式實現的 EARegistryBase           |
//+------------------------------------------------------------------+
class EALogRegistry : public EARegistryBase
{
private:
    EAFileLog* m_logger;  // 日誌記錄器

public:
    // 構造函數
    EALogRegistry(ItemRegistry<void*>* registry, string type = EA_LOG_REGISTRY_TYPE, EAFileLog* logger = NULL)
        : EARegistryBase(registry, type),
          m_logger(logger ? logger : EAFileLog::GetInstance())
    {
    }

    EALogRegistry(EARegistryBase* registry, string type = EA_LOG_REGISTRY_TYPE, EAFileLog* logger = NULL)
        : EARegistryBase(registry, type),
          m_logger(logger ? logger : EAFileLog::GetInstance())
    {
        EARegistryBase::SetRegistry(registry.GetRegistry());
    }

    EALogRegistry(string name, string type = EA_LOG_REGISTRY_TYPE, int maxItems = EA_REGISTRY_BASE_MAX_ITEMS, EAFileLog* logger = NULL)
        : EARegistryBase(name, type, maxItems),
          m_logger(logger ? logger : EAFileLog::GetInstance())
    {
    }

    // 析構函數
    virtual ~EALogRegistry()
    {
        // 不需要刪除 m_logger，因為它是單例
    }

    // 註冊新項目
    virtual RegistryResult<string>* Register(const string name, const string description, void* value) override
    {
        m_logger.Log(DEBUG, "開始註冊項目: " + name);
        RegistryResult<string>* result = EARegistryBase::Register(name, description, value);
        if(result.IsSuccess())
        {
            m_logger.Log(INFO, "註冊項目成功: " + name + ", 鍵: " + result.GetKey());
        }
        else
        {
            m_logger.Log(WARNING, "註冊項目失敗: " + name + ", 原因: " + result.GetMessage());
        }
        return result;
    }

    // 移除項目
    virtual bool Unregister(const string key) override
    {
        m_logger.Log(DEBUG, "開始移除項目: " + key);
        bool result = EARegistryBase::Unregister(key);
        if(result)
        {
            m_logger.Log(INFO, "移除項目成功: " + key);
        }
        else
        {
            m_logger.Log(WARNING, "移除項目失敗: " + key);
        }
        return result;
    }

    // 清空註冊器
    virtual void Clear() override
    {
        m_logger.Log(INFO, "清空註冊器: " + GetName());
        EARegistryBase::Clear();
    }

    // 獲取註冊器名稱
    virtual string GetName() override
    {
        return EARegistryBase::GetName();
    }

    // 獲取項目數量
    virtual int GetCount() override
    {
        int count = EARegistryBase::GetCount();
        m_logger.Log(DEBUG, "註冊器項目數量: " + IntegerToString(count));
        return count;
    }

    // 獲取最大項目數量
    virtual int GetMaxItems() override
    {
        return EARegistryBase::GetMaxItems();
    }

    // 根據Key獲取項目
    virtual RegistryItem<void*>* GetItem(const string key) override
    {
        m_logger.Log(DEBUG, "獲取項目: " + key);
        RegistryItem<void*>* item = EARegistryBase::GetItem(key);
        if(item != NULL)
        {
            m_logger.Log(DEBUG, "獲取項目成功: " + key + ", 名稱: " + item.GetName());
        }
        else
        {
            m_logger.Log(WARNING, "獲取項目失敗: " + key);
        }
        return item;
    }

    // 獲取所有Key
    virtual int GetAllKeys(string &Keys[]) override
    {
        m_logger.Log(DEBUG, "獲取所有鍵");
        int count = EARegistryBase::GetAllKeys(Keys);
        m_logger.Log(DEBUG, "獲取到 " + IntegerToString(count) + " 個鍵");
        return count;
    }

    // 獲取最後註冊的鍵
    virtual string GetLastRegisteredKey() override
    {
        string key = EARegistryBase::GetLastRegisteredKey();
        m_logger.Log(DEBUG, "獲取最後註冊的鍵: " + key);
        return key;
    }

    // 獲取被裝飾的註冊器
    virtual ItemRegistry<void*>* GetRegistry() override
    {
        return EARegistryBase::GetRegistry();
    }

protected:
    // 設置被裝飾的註冊器
    virtual void SetRegistry(ItemRegistry<void*>* registry) override
    {
        m_logger.Log(INFO, "設置註冊器: " + GetName());
        EARegistryBase::SetRegistry(registry);
    }
};
