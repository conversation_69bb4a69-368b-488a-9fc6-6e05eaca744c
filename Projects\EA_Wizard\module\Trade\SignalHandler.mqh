#property strict

#include "../../module/Stage/Enum.mqh"

// 防止重複包含
#ifndef _SIGNAL_HANDLER_MQH_
#define _SIGNAL_HANDLER_MQH_

// // 單一交易信號類型枚舉
// enum ENUM_SIGNAL
// {
//     SIGNAL_NONE = 0,    // 無信號
//     SIGNAL_BUY = 1,     // 買入信號
//     SIGNAL_SELL = -1,   // 賣出信號
//     SIGNAL_CLOSE = 2    // 平倉信號
// };

// 單一交易信號處理類
class SignalHandler
{
private:
    ENUM_SIGNAL m_signal;       // 當前信號
    datetime m_signal_time;     // 信號產生時間
    double m_signal_price;      // 信號產生價格
    double m_signal_strength;   // 信號強度 (0.0-1.0)
    string m_signal_source;     // 信號來源
    bool m_is_confirmed;        // 信號是否已確認
    
public:
    // 建構函數
    SignalHandler(ENUM_SIGNAL signal = SIGNAL_NONE);
    
    // 設置信號
    void SetSignal(ENUM_SIGNAL signal);
    
    // 設置信號（帶附加信息）
    void SetSignal(ENUM_SIGNAL signal, double price, double strength = 0.0, string source = "");
    
    // 獲取信號
    ENUM_SIGNAL GetSignal() const;
    
    // 檢查是否有信號
    bool HasSignal() const;
    
    // 檢查是否為買入信號
    bool IsBuySignal() const;
    
    // 檢查是否為賣出信號
    bool IsSellSignal() const;
    
    // 檢查是否為平倉信號
    bool IsCloseSignal() const;
    
    // 將信號轉換為字符串
    string ToString() const;
    
    // 從字符串轉換為信號
    static ENUM_SIGNAL FromString(const string signal_str);
    
    // 獲取信號描述
    string GetDescription() const;
    
    // 獲取信號產生時間
    datetime GetSignalTime() const;
    
    // 獲取信號產生價格
    double GetSignalPrice() const;
    
    // 獲取信號強度
    double GetSignalStrength() const;
    
    // 獲取信號來源
    string GetSignalSource() const;
    
    // 設置信號確認狀態
    void SetConfirmed(bool confirmed);
    
    // 檢查信號是否已確認
    bool IsConfirmed() const;
    
    // 清除信號
    void Clear();
    
    // 獲取詳細信息
    string GetDetailedInfo() const;
};

// 建構函數
SignalHandler::SignalHandler(ENUM_SIGNAL signal)
{
    m_signal = signal;
    m_signal_time = 0;
    m_signal_price = 0.0;
    m_signal_strength = 0.0;
    m_signal_source = "";
    m_is_confirmed = false;
}

// 設置信號
void SignalHandler::SetSignal(ENUM_SIGNAL signal)
{
    m_signal = signal;
    m_signal_time = TimeCurrent();
    m_signal_price = Close[0];
    m_signal_strength = 0.0;
    m_signal_source = "Default";
    m_is_confirmed = false;
}

// 設置信號（帶附加信息）
void SignalHandler::SetSignal(ENUM_SIGNAL signal, double price, double strength = 0.0, string source = "")
{
    m_signal = signal;
    m_signal_time = TimeCurrent();
    m_signal_price = price;
    m_signal_strength = strength;
    m_signal_source = source;
    m_is_confirmed = false;
}

// 獲取信號
ENUM_SIGNAL SignalHandler::GetSignal() const
{
    return m_signal;
}

// 檢查是否有信號
bool SignalHandler::HasSignal() const
{
    return m_signal != SIGNAL_NONE;
}

// 檢查是否為買入信號
bool SignalHandler::IsBuySignal() const
{
    return m_signal == SIGNAL_BUY;
}

// 檢查是否為賣出信號
bool SignalHandler::IsSellSignal() const
{
    return m_signal == SIGNAL_SELL;
}

// 檢查是否為平倉信號
bool SignalHandler::IsCloseSignal() const
{
    return m_signal == SIGNAL_CLOSE;
}

// 將信號轉換為字符串
string SignalHandler::ToString() const
{
    switch(m_signal)
    {
        case SIGNAL_NONE:  return "SIGNAL_NONE";
        case SIGNAL_BUY:   return "SIGNAL_BUY";
        case SIGNAL_SELL:  return "SIGNAL_SELL";
        case SIGNAL_CLOSE: return "SIGNAL_CLOSE";
        default:           return "UNKNOWN";
    }
}

// 從字符串轉換為信號
ENUM_SIGNAL SignalHandler::FromString(const string signal_str)
{
    if(signal_str == "SIGNAL_NONE")  return SIGNAL_NONE;
    if(signal_str == "SIGNAL_BUY")   return SIGNAL_BUY;
    if(signal_str == "SIGNAL_SELL")  return SIGNAL_SELL;
    if(signal_str == "SIGNAL_CLOSE") return SIGNAL_CLOSE;
    
    return SIGNAL_NONE; // 默認返回無信號
}

// 獲取信號描述
string SignalHandler::GetDescription() const
{
    switch(m_signal)
    {
        case SIGNAL_NONE:  return "無信號";
        case SIGNAL_BUY:   return "買入信號";
        case SIGNAL_SELL:  return "賣出信號";
        case SIGNAL_CLOSE: return "平倉信號";
        default:           return "未知信號";
    }
}

// 獲取信號產生時間
datetime SignalHandler::GetSignalTime() const
{
    return m_signal_time;
}

// 獲取信號產生價格
double SignalHandler::GetSignalPrice() const
{
    return m_signal_price;
}

// 獲取信號強度
double SignalHandler::GetSignalStrength() const
{
    return m_signal_strength;
}

// 獲取信號來源
string SignalHandler::GetSignalSource() const
{
    return m_signal_source;
}

// 設置信號確認狀態
void SignalHandler::SetConfirmed(bool confirmed)
{
    m_is_confirmed = confirmed;
}

// 檢查信號是否已確認
bool SignalHandler::IsConfirmed() const
{
    return m_is_confirmed;
}

// 清除信號
void SignalHandler::Clear()
{
    m_signal = SIGNAL_NONE;
    m_signal_time = 0;
    m_signal_price = 0.0;
    m_signal_strength = 0.0;
    m_signal_source = "";
    m_is_confirmed = false;
}

// 獲取詳細信息
string SignalHandler::GetDetailedInfo() const
{
    string info = "信號: " + GetDescription() + "\n";
    info += "時間: " + TimeToString(m_signal_time, TIME_DATE|TIME_MINUTES|TIME_SECONDS) + "\n";
    info += "價格: " + DoubleToString(m_signal_price, Digits) + "\n";
    info += "強度: " + DoubleToString(m_signal_strength * 100, 2) + "%\n";
    info += "來源: " + m_signal_source + "\n";
    info += "確認: " + (m_is_confirmed ? "是" : "否");
    
    return info;
}

#endif // _SIGNAL_HANDLER_MQH_
