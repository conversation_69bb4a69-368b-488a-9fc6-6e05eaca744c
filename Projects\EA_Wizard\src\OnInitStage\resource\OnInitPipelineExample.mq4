//+------------------------------------------------------------------+
//|                                         OnInitPipelineExample.mq4 |
//|                                                       EA_Wizard    |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "OnInitStage.mqh"

// 輸入參數
input int MagicNumber = 12345;        // 魔術數字
input double LotSize = 0.01;          // 交易量
input int Slippage = 3;               // 滑點
input double StopLoss = 20.0;         // 止損點數
input double TakeProfit = 40.0;       // 止盈點數
input double MaxRiskPercent = 2.0;    // 最大風險百分比
input int MaxPositions = 5;           // 最大持倉數量
input int MAFastPeriod = 10;          // 快速移動平均線週期
input int MASlowPeriod = 20;          // 慢速移動平均線週期
input ENUM_MA_METHOD MAMethod = MODE_SMA; // 移動平均線方法
input int RSIPeriod = 14;             // RSI週期
input double RSIOverbought = 70.0;    // RSI超買水平
input double RSIOversold = 30.0;      // RSI超賣水平

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // 創建初始化流水線處理器
    InitPipelineProcessor* processor = new InitPipelineProcessor(Symbol());
    
    // 執行初始化流水線
    ENUM_INIT_RETCODE result = processor.Execute();
    
    // 處理初始化結果
    switch (result)
    {
    case INIT_PARAMETERS_INCORRECT:
        Print("EA初始化失敗，原因: 參數錯誤");
        delete processor;
        return(result);
    case INIT_FAILED:
        Print("EA初始化失敗，原因: 初始化失敗");
        delete processor;
        return(result);
    }
    
    Print("EA初始化成功");
    delete processor;
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("EA卸載，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這裡是 OnTick 處理邏輯
    Print("OnTick 執行");
}
//+------------------------------------------------------------------+
