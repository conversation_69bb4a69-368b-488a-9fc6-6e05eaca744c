#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"

// 初始化技術指標階段
class InitIndicatorsStage : public OnInitPipeline
{
private:
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;
    int m_ma_period_fast;
    int m_ma_period_slow;
    int m_ma_shift;
    ENUM_MA_METHOD m_ma_method;
    ENUM_APPLIED_PRICE m_applied_price;
    
public:
    // 建構函數
    InitIndicatorsStage(string symbol = NULL, 
                        ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                        int ma_period_fast = 10,
                        int ma_period_slow = 20,
                        int ma_shift = 0,
                        ENUM_MA_METHOD ma_method = MODE_SMA,
                        ENUM_APPLIED_PRICE applied_price = PRICE_CLOSE)
        : m_symbol(symbol == NULL ? Symbol() : symbol),
          m_timeframe(timeframe == PERIOD_CURRENT ? Period() : timeframe),
          m_ma_period_fast(ma_period_fast),
          m_ma_period_slow(ma_period_slow),
          m_ma_shift(ma_shift),
          m_ma_method(ma_method),
          m_applied_price(applied_price)
    {
    }
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 初始化移動平均線指標
        int ma_fast_handle = iMA(m_symbol, m_timeframe, m_ma_period_fast, m_ma_shift, m_ma_method, m_applied_price);
        int ma_slow_handle = iMA(m_symbol, m_timeframe, m_ma_period_slow, m_ma_shift, m_ma_method, m_applied_price);
        
        // 檢查指標是否初始化成功
        if(ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE)
        {
            Print("移動平均線指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 初始化 RSI 指標
        int rsi_period = 14;
        int rsi_handle = iRSI(m_symbol, m_timeframe, rsi_period, m_applied_price);
        
        if(rsi_handle == INVALID_HANDLE)
        {
            Print("RSI 指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 初始化 MACD 指標
        int macd_fast_ema = 12;
        int macd_slow_ema = 26;
        int macd_signal = 9;
        int macd_handle = iMACD(m_symbol, m_timeframe, macd_fast_ema, macd_slow_ema, macd_signal, m_applied_price);
        
        if(macd_handle == INVALID_HANDLE)
        {
            Print("MACD 指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 將指標參數註冊到註冊器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        
        // 註冊移動平均線參數
        registry.Register("MA_Fast_Period", "快速移動平均線週期", &m_ma_period_fast);
        registry.Register("MA_Slow_Period", "慢速移動平均線週期", &m_ma_period_slow);
        registry.Register("MA_Method", "移動平均線方法", &m_ma_method);
        registry.Register("MA_Applied_Price", "移動平均線應用價格", &m_applied_price);
        
        // 註冊 RSI 參數
        registry.Register("RSI_Period", "RSI 週期", &rsi_period);
        
        // 註冊 MACD 參數
        registry.Register("MACD_Fast_EMA", "MACD 快速 EMA", &macd_fast_ema);
        registry.Register("MACD_Slow_EMA", "MACD 慢速 EMA", &macd_slow_ema);
        registry.Register("MACD_Signal", "MACD 信號線", &macd_signal);
        
        Print("技術指標初始化成功");
        return INIT_SUCCEEDED;
    }
};
