# Pipeline 模組概述

## 簡介

Pipeline 模組是 EA_Wizard 專案的核心組件，實現了流水線設計模式，用於處理 EA 的初始化、交易執行和清理等操作。該模組允許將複雜的處理邏輯分解為一系列獨立的階段，每個階段專注於特定的任務，從而提高代碼的可維護性和可擴展性。

## 核心概念

### 流水線 (Pipeline)

流水線是一系列按順序執行的處理階段的集合。每個階段接收輸入，執行特定的處理邏輯，然後產生輸出。流水線的主要優點是：

1. **模組化設計**：將複雜的處理邏輯分解為獨立的階段
2. **可重用性**：階段可以在不同的流水線中重複使用
3. **可擴展性**：可以輕鬆添加、移除或替換階段
4. **可測試性**：每個階段可以獨立測試

### 階段 (Stage)

階段是流水線中的基本處理單元，實現了 `IPipeline` 介面。每個階段都有一個 `Execute` 方法，接收輸入並產生輸出。階段可以是：

1. **獨立階段**：不依賴其他階段的結果
2. **依賴階段**：依賴其他階段的結果，通常通過註冊器獲取數據

### 管理器 (Manager)

管理器負責組織和執行流水線中的階段。它提供了添加、移除和執行階段的方法。管理器的主要功能包括：

1. **階段管理**：添加和移除階段
2. **流水線執行**：按順序執行所有階段
3. **錯誤處理**：處理階段執行過程中的錯誤
4. **註冊器集成**：與註冊器集成，用於階段間的數據共享

### 註冊器 (Registry)

註冊器用於在不同階段之間共享數據。它提供了註冊、獲取和管理數據的方法。註冊器的主要功能包括：

1. **數據註冊**：註冊數據到註冊器
2. **數據獲取**：從註冊器獲取數據
3. **數據管理**：管理註冊器中的數據項

## 模組架構

Pipeline 模組的架構由以下主要組件組成：

1. **IPipeline**：流水線介面，定義了 `Execute` 方法
2. **PipelineGroupBase**：流水線群組基類，實現了 `IPipeline` 介面，提供了階段管理功能
3. **PipelineManager**：流水線管理器，繼承自 `PipelineGroupBase`，提供了流水線執行和錯誤處理功能
4. **RegistrablePipeline**：可註冊流水線，實現了 `IPipeline` 介面，提供了與註冊器集成的功能
5. **特定流水線類型**：如 `OnInitPipeline`、`OnTickPipeline` 和 `OnDeinitPipeline`，用於處理特定類型的操作
6. **特定流水線管理器**：如 `OnInitPipelineManager`、`OnTickPipelineManager` 和 `OnDeinitPipelineManager`，用於管理特定類型的流水線

## 使用方法

### 創建流水線階段

```cpp
// 創建 OnInit 流水線階段
class ParameterReadStage : public OnInitPipeline
{
public:
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 讀取參數邏輯
        Print("讀取參數");
        return INIT_SUCCEEDED;
    }
};
```

### 創建流水線處理器

```cpp
// 創建 OnInit 流水線處理器
class InitPipelineProcessor : public OnInitPipelineManager
{
public:
    InitPipelineProcessor(string symbol = NULL) {
        // 設置註冊器的鍵字頭
        GetRegistry().SetKeyPrefix("Init");

        // 交易品種
        string trade_symbol = (symbol == NULL) ? Symbol() : symbol;

        // 添加初始化階段
        AddPipeline(new OnInitStartStage());
        AddPipeline(new ParameterReadStage());
        AddPipeline(new VariableInitStage(trade_symbol));
        AddPipeline(new TradingEnvironmentCheckStage());
        AddPipeline(new IndicatorInitStage(trade_symbol));
        AddPipeline(new OnInitEndStage());
    }
};
```

### 使用流水線處理器

```cpp
// 在 OnInit 函數中使用流水線處理器
int OnInit()
{
    // 創建初始化流水線處理器
    InitPipelineProcessor* processor = new InitPipelineProcessor(Symbol());
    
    // 執行初始化流水線
    ENUM_INIT_RETCODE result = processor.Execute();
    
    // 處理初始化結果
    switch (result)
    {
    case INIT_PARAMETERS_INCORRECT:
        Print("EA初始化失敗，原因: 參數錯誤");
        delete processor;
        return(result);
    case INIT_FAILED:
        Print("EA初始化失敗，原因: 初始化失敗");
        delete processor;
        return(result);
    }
    
    Print("EA初始化成功");
    delete processor;
    return(INIT_SUCCEEDED);
}
```

## 最佳實踐

1. **階段設計**：每個階段應該專注於一個特定的任務，遵循單一職責原則
2. **錯誤處理**：在階段中妥善處理錯誤，並返回適當的結果
3. **資源管理**：在流水線執行完成後釋放資源，避免內存洩漏
4. **數據共享**：使用註冊器在階段之間共享數據，避免使用全局變量
5. **命名規範**：使用清晰的命名規範，使代碼更易於理解和維護

## 擴展方向

1. **並行執行**：支持階段的並行執行，提高性能
2. **條件執行**：根據條件選擇性地執行某些階段
3. **動態配置**：支持在運行時動態配置流水線
4. **監控和日誌**：添加監控和日誌功能，方便調試和優化
5. **可視化**：提供流水線執行過程的可視化展示
