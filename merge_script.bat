@echo off
cd /d "c:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard"
git --no-pager checkout dev > merge_output.txt 2>&1
git --no-pager merge feature/PipelineAdvance >> merge_output.txt 2>&1
git --no-pager status >> merge_output.txt 2>&1
git --no-pager log --oneline -n 5 >> merge_output.txt 2>&1
echo Merge completed. Check merge_output.txt for details.
