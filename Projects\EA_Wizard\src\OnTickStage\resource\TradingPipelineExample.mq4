//+------------------------------------------------------------------+
//|                                         OnTickPipelineExample.mq4 |
//|                                                       EA_Wizard    |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "InitCheckStage.mqh"
#include "MarketDataStage.mqh"
#include "OrderScanStage.mqh"
#include "SignalGenerationStage.mqh"
#include "TradeExecutionStage.mqh"
#include "../../module/Pipeline/OnTickPipeline.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    // 創建交易流水線管理器
    OnTickPipelineManager* manager = new OnTickPipelineManager();
    
    // 設置註冊器的鍵字頭
    manager.GetRegistry().SetKeyPrefix("Trading");
    
    // 獲取管理器ID
    string managerId = manager.GetId();
    Print("管理器ID: ", managerId);
    
    // 創建流水線階段
    InitCheckStage* initCheck = new InitCheckStage();
    MarketDataStage* marketData = new MarketDataStage();
    OrderScanStage* orderScan = new OrderScanStage(12345); // 使用魔術數字12345
    SignalGenerationStage* signalGen = new SignalGenerationStage();
    TradeExecutionStage* tradeExec = new TradeExecutionStage(signalGen);
    
    // 添加階段到流水線
    manager.AddPipeline(initCheck);
    manager.AddPipeline(marketData);
    manager.AddPipeline(orderScan);
    manager.AddPipeline(signalGen);
    manager.AddPipeline(tradeExec);
    
    // 執行流水線
    bool result = manager.Execute();
    
    // 輸出結果
    Print("流水線執行結果: ", (result ? "成功" : "失敗"));
    
    // 獲取註冊器中的所有項目
    string ids[];
    manager.GetRegistry().GetAllIds(ids);
    
    Print("註冊器中的項目數量: ", ArraySize(ids));
    
    // 遍歷所有項目
    for(int i = 0; i < ArraySize(ids); i++)
    {
        IRegistryItem<PipelineManager<bool, void*>*>* item = manager.GetRegistry().GetItem(ids[i]);
        if(item != NULL)
        {
            Print("項目 ID: ", ids[i], ", 名稱: ", item.GetName());
        }
    }
    
    // 清理資源
    delete manager; // 這將自動刪除所有流水線階段
    
    Print("交易流水線示例執行完成！");
}
//+------------------------------------------------------------------+
