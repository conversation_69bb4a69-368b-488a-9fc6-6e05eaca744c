#property strict

struct MqlOrder{
    int type;
    double volume;
    double price;
    int slippage;
    double stoploss;
    double takeprofit;
    string comment;
    int magic;
    datetime expiration;
    color arrowcolor;

    void Initialize() {
        type = 0;
        volume = 0;
        price = 0;
        slippage = 0;
        stoploss = 0;
        takeprofit = 0;
        comment = "";
        magic = 0;
        expiration = 0;
        arrowcolor = clrNONE;
    }

    bool ValidateData()
  {
   if(this.type < 0) return false;
   if(this.volume <= 0) return false;
   if(this.price <= 0) return false;
   if(this.slippage < 0) return false;
   if(this.stoploss < 0) return false;
   if(this.takeprofit < 0) return false;
  //  if(this.comment == "") return false;
   if(this.magic < 0) return false;
   if(this.expiration < 0) return false;
  //  if(this.arrowcolor == CLR_NONE) return false;
   return true;
  }
};

struct MqlClose{
    int ticket;
    double lots;
    double price;
    int slippage;
    color arrowcolor;

    void Initialize() {
        ticket = 0;
        lots = 0;
        price = 0;
        slippage = 0;
        arrowcolor = clrNONE;
    }

    bool ValidateData()
  {
   if(this.ticket <= 0) return false;
   if(this.lots <= 0) return false;
   if(this.price <= 0) return false;
   if(this.slippage < 0) return false;
  //  if(this.arrowcolor == CLR_NONE) return false;
   return true;
  }
};

class MqlOrderWrapper{
  private:
    MqlOrder m_order;

  public:
    MqlOrderWrapper() {
        m_order.Initialize();
    }

    ~MqlOrderWrapper() {
    }

    bool ValidateData() {
        return m_order.ValidateData();
    }

    void Initialize() {
        m_order.Initialize();
    }

    void GetOrder(MqlOrder& order) {
        order = m_order;
    }

    void SetOrder(const MqlOrder& order) {
        m_order = order;
    }
};

class MqlCloseWrapper{
  private:
    MqlClose m_close;

  public:
    MqlCloseWrapper() {
        m_close.Initialize();
    }

    ~MqlCloseWrapper() {
    }

    bool ValidateData() {
        return m_close.ValidateData();
    }

    void Initialize() {
        m_close.Initialize();
    }

    void GetClose(MqlClose& close) {
        close = m_close;
    }

    void SetClose(const MqlClose& close) {
        m_close = close;
    }
};
