#property strict

#include "../../module/Stage/Stage.mqh"

// 初始化檢查階段
class InitCheck : public OnInitStage
{
public:
    InitCheck() : OnInitStage(ONINIT_STAGE_START){}
    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化檢查階段開始");

        int maxRetries = 4;
        int retryCount = 0;

        while((!IsTradeAllowed() || !IsConnected()) && retryCount < maxRetries) {
            if(!IsTradeAllowed())
                Print("交易不允許");
            
            if(!IsConnected())
                Print("未連接到交易伺服器");
            
            Sleep(5000);
            retryCount++;
        }

        if(retryCount >= maxRetries) {
            Print("初始化檢查階段失敗，超過最大重試次數");
            Alert("初始化檢查階段失敗，超過最大重試次數");
            return INIT_FAILED;
        }

        Print("初始化檢查階段結束");
        return INIT_SUCCEEDED;
    }
}init_check_stage;
