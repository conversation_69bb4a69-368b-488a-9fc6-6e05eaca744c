//+------------------------------------------------------------------+
//|                                              PointerRegistry.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Registry.mqh"
#include "../mql4-lib-master/Lang/Pointer.mqh"
#include "../mql4-lib-master/Lang/Hash.mqh"

//+------------------------------------------------------------------+
//| 指針註冊器類 - 用於註冊和管理指針類型的對象                    |
//+------------------------------------------------------------------+
template<typename Val>
class PointerRegistry : public Registry<void*, Val>
{
protected:
    string m_keyPrefix;                     // 鍵的字頭
    void*  m_lastGeneratedKey;         // 最後生成的鍵

public:
    // 公開構造函數（移除單例模式）
    PointerRegistry(const string name = "PointerRegistry", const int maxItems = 100)
        : Registry<void*, Val>(name, maxItems)
    {
        m_keyPrefix = "PR"; // 預設字頭
        m_lastGeneratedKey = NULL;
    }

    ~PointerRegistry() {
    }



    // 生成唯一ID
    void* GenerateKey() override
    {
        return m_lastGeneratedKey;
    }

    // 設置鍵的字頭
    void SetKeyPrefix(const string prefix)
    {
        m_keyPrefix = prefix;
    }

    // 獲取鍵的字頭
    string GetKeyPrefix() const
    {
        return m_keyPrefix;
    }

    // 將鍵轉換為字符串
    string KeyToString(const void* id) override
    {
        // 使用地址作為字符串表示
        long address = (long)GetAddress(id);
        return m_keyPrefix + "_" + IntegerToString(address);
    }

    // 註冊新項目
    bool Register(const string name, const string description, Val value, void* key = NULL) override
    {
        // 檢查是否達到最大項目數量
        if(m_items.size() >= m_maxItems)
        {
            Print("註冊失敗：已達到最大項目數量 ", m_maxItems);
            return false;
        }

        // 生成唯一ID或使用提供的鍵
        void* id;
        if(key == NULL)
        {
            m_lastGeneratedKey = value;
            id = GenerateKey();
        }
        else
        {
            id = key;
            // 檢查鍵是否已存在
            if(m_items.contains(id))
            {
                Print("註冊失敗：鍵已存在");
                return false;
            }
        }

        // 創建新項目
        IRegistryItem<Val>* item = new RegistryItem<Val>(KeyToString(id), name, description, value);

        // 添加到哈希表
        m_items.set(id, item);

        // 保存最後註冊的鍵
        m_lastRegisteredKey = id;

        Print("註冊成功：", item.ToString());
        return true;
    }
};


