//+------------------------------------------------------------------+
//|                                                 RegistryItem.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| 註冊項目基本結構 - 實現接口                                      |
//+------------------------------------------------------------------+
template<typename Val>
class RegistryItem
{
private:
    string m_id;           // 項目唯一標識符
    string m_name;         // 項目名稱
    string m_description;  // 項目描述
    Val m_value;           // 項目值
    datetime m_createTime; // 創建時間
    string m_type;         // 項目類型

public:
    // 構造函數
    RegistryItem(const string id, const string name, const string description, Val value, const string type = "")
        : m_id(id), m_name(name), m_description(description), m_value(value), m_type(type)
    {
        m_createTime = TimeCurrent();
    }

    // 析構函數
    ~RegistryItem()
    {
        // 不需要釋放資源
    }

    // 獲取項目ID
    string GetId() const { return m_id; }

    // 獲取項目名稱
    string GetName() const { return m_name; }

    // 獲取項目描述
    string GetDescription() const { return m_description; }

    // 獲取項目值
    Val GetValue() const { return m_value; }

    // 獲取創建時間
    datetime GetCreateTime() const { return m_createTime; }

    // 獲取項目類型
    string GetType() const { return m_type; }

    // 轉換為字符串（用於調試）
    string ToString() const
    {
        return StringFormat("ID: %s, Name: %s, Description: %s, Type: %s, CreateTime: %s",
                           m_id, m_name, m_description, m_type,
                           TimeToString(m_createTime));
    }
};
