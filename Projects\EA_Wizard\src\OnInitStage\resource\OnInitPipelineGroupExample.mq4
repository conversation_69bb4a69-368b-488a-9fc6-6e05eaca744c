//+------------------------------------------------------------------+
//|                                   OnInitPipelineGroupExample.mq4 |
//|                                                       EA_Wizard    |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // 創建流水線階段
    OnInitStartStage* startStage = new OnInitStartStage();
    ParameterReadStage* paramStage = new ParameterReadStage();
    VariableInitStage* varStage = new VariableInitStage();
    TradingEnvironmentCheckStage* envStage = new TradingEnvironmentCheckStage();
    IndicatorInitStage* indStage = new IndicatorInitStage();
    OnInitEndStage* endStage = new OnInitEndStage();
    
    // 創建流水線管理器
    OnInitPipelineManager* manager = new OnInitPipelineManager();
    
    // 添加階段到流水線
    manager.AddPipeline(startStage);
    manager.AddPipeline(paramStage);
    manager.AddPipeline(varStage);
    manager.AddPipeline(envStage);
    manager.AddPipeline(indStage);
    manager.AddPipeline(endStage);
    
    // 執行流水線
    ENUM_INIT_RETCODE result = manager.Execute();
    
    // 處理結果
    switch(result)
    {
    case INIT_PARAMETERS_INCORRECT:
        Print("EA初始化失敗，原因: 參數錯誤");
        delete manager;
        return(result);
    case INIT_FAILED:
        Print("EA初始化失敗，原因: 初始化失敗");
        delete manager;
        return(result);
    }
    
    Print("EA初始化成功");
    delete manager;
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("EA卸載，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這裡是 OnTick 處理邏輯
    Print("OnTick 執行");
}
//+------------------------------------------------------------------+
