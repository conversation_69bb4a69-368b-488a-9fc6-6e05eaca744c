//+------------------------------------------------------------------+
//|                                       PipelineStateRunning.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "PipelineStateBase.mqh"
#include "../state/PipelineStateCompleted.mqh"
#include "../state/PipelineStateFailed.mqh"

//+------------------------------------------------------------------+
//| 流水線執行中狀態 - 實現執行中狀態的行為                           |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStateRunning : public PipelineStateBase<PipelineObj>
{
public:
    // 構造函數
    PipelineStateRunning()
    : PipelineStateBase(PIPELINE_STATE_RUNNING, "執行中") {}
    
    // 析構函數
    ~PipelineStateRunning() {}
    
    // 執行流水線
    void Execute(PipelineObj pipeline) override;
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline) override;
};

template <typename PipelineObj>
void PipelineStateRunning::Execute(PipelineObj pipeline)
{
    // 執行中狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經在執行中，不能重複執行");
}

template <typename PipelineObj>
void PipelineStateRunning::Restore(PipelineObj pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new PipelineStatePending<PipelineObj>());
}
