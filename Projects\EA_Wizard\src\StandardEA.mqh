//+------------------------------------------------------------------+
//|                                                   StandardEA.mq4 |
//|                                                    EA_Wizard     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "EAInitializer.mqh"
#include "EAProcessor.mqh"
// #include "EADeinitializer.mqh"

//--- 輸入參數
input double   Lots = 0.1;           // 交易手數
input int      StopLoss = 100;       // 止損點數
input int      TakeProfit = 200;     // 獲利點數
input int      MagicNumber = 12345;  // 魔術數字

//--- 全局變量
int OnInit()
{
   // 初始化代碼
   ENUM_INIT_RETCODE result = EAInitializer();

   return(result);
}

void OnDeinit(const int reason)
{
   // // 執行清理代碼
   // bool result = EADeinitializer(reason);

   // if(!result)
   // {
   //    Print("EA清理失敗");
   // }

   // // 清理代碼
   // Print("EA已停止，原因代碼: ", reason);
}

void OnTick()
{
   // 執行 EA 處理器
   bool result = EAProcessor();

   // 如果處理失敗，輸出警告
   if(!result)
   {
      Print("OnTick 處理失敗");
   }
}

//+------------------------------------------------------------------+
//| 自定義函數                                                        |
//+------------------------------------------------------------------+
// 您可以在這裡添加自定義函數
