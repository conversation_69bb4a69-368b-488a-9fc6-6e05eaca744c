#property strict

// 流水線介面
template <typename Output, typename Input>
interface IPipeline
{
    Output Execute(Input in);
    bool IsError();
    string LastError();

};

// 範例流水線階段
class SampleStage : public IPipeline<bool, int>
{
public:
    bool Execute(int in) override
    {
        // 實現您的流水線階段邏輯，並返回結果
        return true;
    }
    bool IsError() override
    {
        // 實現您的錯誤檢查邏輯，並返回結果
        return false;
    }
    string LastError() override
    {
        // 實現您的錯誤檢查邏輯，並返回結果
        return "No Error";
    }
};

template <typename Output, typename Input>
interface IPipelineGroup : public IPipeline<Output, Input>
{
    bool HasPipeline(IPipeline<Output, Input>* pipeline) const;
    bool AddPipeline(IPipeline<Output, Input>* pipeline);
    bool RemovePipeline(IPipeline<Output, Input>* pipeline);
    void ClearPipelines();
    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const;
};

// 流水線群組基類
template <typename Output, typename Input>
class PipelineGroupBase : public IPipelineGroup<Output, Input>
{
private:
    IPipeline<Output, Input>* m_pipelines[];
protected:
    bool m_is_error;
    string m_last_error;

public:
    // 建構函數
    PipelineGroupBase() {
        m_is_error = false;
        m_last_error = "No Error";
    }
    PipelineGroupBase(IPipeline<Output, Input>* &pipelines[])
    {
        // 初始化階段
        for(int i = 0; i < ArraySize(pipelines); i++)
        {
            m_pipelines[i] = pipelines[i];
        }
        m_is_error = false;
        m_last_error = "No Error";
    }

    // 解構函數
    ~PipelineGroupBase()
    {
        // 釋放階段記憶體
        ArrayFree(m_pipelines);
    }

    bool IsError() override
    {
        return m_is_error;
    }

    string LastError() override
    {
        return m_last_error;
    }

    bool HasPipeline(IPipeline<Output, Input>* pipeline) const
    {
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                return true;
            }
        }
        return false;
    }

    bool AddPipeline(IPipeline<Output, Input>* pipeline)
    {
        if(HasPipeline(pipeline)){
            // 如果流水線階段已存在，則返回錯誤
            m_is_error = true;
            m_last_error = "流水線階段已存在！";
            Print("流水線階段已存在！");
            return false;
        }

        ArrayResize(m_pipelines, ArraySize(m_pipelines) + 1);
        m_pipelines[ArraySize(m_pipelines) - 1] = pipeline;
        return true; // 返回新增階段的索引
    }

    bool RemovePipeline(IPipeline<Output, Input>* pipeline)
    {
        if(!HasPipeline(pipeline)){
            // 如果流水線階段不存在，則返回錯誤
            m_is_error = true;
            m_last_error = "流水線階段不存在！";
            Print("流水線階段不存在！");
            return false;
        }

        int size = ArraySize(m_pipelines);
        for(int i = 0; i < size; i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                // 移動後面的元素往前
                if(i < size-1)
                {
                    for(int j = i; j < size-1; j++)
                    {
                        m_pipelines[j] = m_pipelines[j+1];
                    }
                }
                // 調整陣列大小
                ArrayResize(m_pipelines, size-1);
                return true;
            }
        }
        return false;
    }

    void ClearPipelines()
    {
        ArrayFree(m_pipelines);
    }

    virtual Output Execute(Input in) override
    {
        // 實現您的流水線群組邏輯，並返回結果
        return NULL;
    }

    int GetPipelines(IPipeline<Output, Input>* &pipelines[]) const
    {
        int size = ArraySize(m_pipelines);
        ArrayResize(pipelines, size);

        for(int i = 0; i < size; i++)
        {
            pipelines[i] = m_pipelines[i];
        }
        return size;
    }
};
