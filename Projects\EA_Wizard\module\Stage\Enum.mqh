//+------------------------------------------------------------------+
//|                                                       Enum.mqh |
//|                                                      EA_Wizard |
//|                                                                |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| EA_Wizard 階段模組枚舉定義                                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| OnInit 階段類型枚舉                                              |
//+------------------------------------------------------------------+
enum ENUM_ONINIT_STAGE
{
    ONINIT_STAGE_START,                // OnInit 開始階段
    ONINIT_STAGE_PARAMETER_READ,       // 參數讀取階段
    ONINIT_STAGE_VARIABLE_INIT,        // 變數初始化階段
    ONINIT_STAGE_TRADING_ENV_CHECK,    // 交易環境檢查階段
    ONINIT_STAGE_INDICATOR_INIT,       // 指標初始化階段
    ONINIT_STAGE_END,                  // OnInit 結束階段
    ONINIT_STAGE_CUSTOM                // 自定義階段
};

//+------------------------------------------------------------------+
//| OnTick 階段類型枚舉                                              |
//+------------------------------------------------------------------+
enum ENUM_ONTICK_STAGE
{
    ONTICK_STAGE_START,                // OnTick 開始階段
    ONTICK_STAGE_MARKET_DATA,          // 市場數據更新階段
    ONTICK_STAGE_POSITION_CHECK,       // 持倉狀態檢查階段
    ONTICK_STAGE_SIGNAL_CHECK,         // 信號檢查階段
    ONTICK_STAGE_RISK_CHECK,           // 風險控制檢查階段
    ONTICK_STAGE_TRADE_EXECUTION,      // 交易執行階段
    ONTICK_STAGE_LOGGING,              // 日誌記錄和通知階段
    ONTICK_STAGE_END,                  // OnTick 結束階段
    ONTICK_STAGE_CUSTOM                // 自定義階段
};

//+------------------------------------------------------------------+
//| OnDeinit 階段類型枚舉                                            |
//+------------------------------------------------------------------+
enum ENUM_ONDEINIT_STAGE
{
    ONDEINIT_STAGE_CLEANUP,            // 清理階段
    ONDEINIT_STAGE_LOGGING,            // 日誌記錄階段
    ONDEINIT_STAGE_CUSTOM              // 自定義階段
};

//+------------------------------------------------------------------+
//| 階段狀態枚舉                                                     |
//+------------------------------------------------------------------+
enum ENUM_STAGE_STATUS
{
    STAGE_STATUS_PENDING,              // 待執行
    STAGE_STATUS_RUNNING,              // 執行中
    STAGE_STATUS_COMPLETED,            // 已完成
    STAGE_STATUS_FAILED,               // 執行失敗
    STAGE_STATUS_SKIPPED               // 已跳過
};

//+------------------------------------------------------------------+
//| 階段執行結果枚舉                                                 |
//+------------------------------------------------------------------+
enum ENUM_STAGE_RESULT
{
    STAGE_RESULT_SUCCESS,              // 執行成功
    STAGE_RESULT_FAILURE,              // 執行失敗
    STAGE_RESULT_PARTIAL_SUCCESS,      // 部分成功
    STAGE_RESULT_RETRY_NEEDED          // 需要重試
};

//+------------------------------------------------------------------+
//| 階段優先級枚舉                                                   |
//+------------------------------------------------------------------+
enum ENUM_STAGE_PRIORITY
{
    STAGE_PRIORITY_CRITICAL = 0,       // 關鍵優先級（最高）
    STAGE_PRIORITY_HIGH = 1,           // 高優先級
    STAGE_PRIORITY_NORMAL = 2,         // 正常優先級
    STAGE_PRIORITY_LOW = 3,            // 低優先級
    STAGE_PRIORITY_BACKGROUND = 4      // 背景優先級（最低）
};

//+------------------------------------------------------------------+
//| 階段執行模式枚舉                                                 |
//+------------------------------------------------------------------+
enum ENUM_STAGE_EXECUTION_MODE
{
    STAGE_EXECUTION_SEQUENTIAL,        // 順序執行（預設）
    STAGE_EXECUTION_CONDITIONAL,       // 條件執行
    STAGE_EXECUTION_PARALLEL           // 並行執行（僅概念性，MQL4不支持真正的並行）
};

//+------------------------------------------------------------------+
//| 階段依賴關係類型枚舉                                             |
//+------------------------------------------------------------------+
enum ENUM_STAGE_DEPENDENCY_TYPE
{
    STAGE_DEPENDENCY_REQUIRED,         // 必須依賴（前置階段必須成功）
    STAGE_DEPENDENCY_OPTIONAL,         // 可選依賴（前置階段可以失敗）
    STAGE_DEPENDENCY_NONE              // 無依賴
};

//+------------------------------------------------------------------+
//| 階段日誌級別枚舉                                                 |
//+------------------------------------------------------------------+
enum ENUM_STAGE_LOG_LEVEL
{
    STAGE_LOG_NONE = 0,                // 不記錄日誌
    STAGE_LOG_ERROR = 1,               // 僅記錄錯誤
    STAGE_LOG_WARNING = 2,             // 記錄警告和錯誤
    STAGE_LOG_INFO = 3,                // 記錄信息、警告和錯誤
    STAGE_LOG_DEBUG = 4,               // 記錄所有內容（包括調試信息）
    STAGE_LOG_VERBOSE = 5              // 詳細記錄所有內容
};

// //+------------------------------------------------------------------+
// //| 交易信號類型枚舉                                                 |
// //+------------------------------------------------------------------+
// enum ENUM_SIGNAL_TYPE
// {
//     SIGNAL_NONE = 0,                   // 無信號
//     SIGNAL_BUY = 1,                    // 買入信號
//     SIGNAL_SELL = -1                   // 賣出信號
// };

// 單一交易信號類型枚舉
enum ENUM_SIGNAL
{
    SIGNAL_NONE = 0,    // 無信號
    SIGNAL_BUY = 1,     // 買入信號
    SIGNAL_SELL = -1,   // 賣出信號
    SIGNAL_CLOSE = 2    // 平倉信號
};
