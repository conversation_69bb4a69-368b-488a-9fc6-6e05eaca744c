//+------------------------------------------------------------------+
//|                                                    Registry.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib-master/Collection/HashMap.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"
#include "interface/IRegistry.mqh"
#include "model/RegistryItem.mqh"
#include "model/RegistryResult.mqh"

//+------------------------------------------------------------------+
//| 註冊器基類 - 用於註冊和管理各種類型的對象                       |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
class Registry : public IRegistry<Key, Val>
{
protected:
    string m_name;                   // 註冊器名稱
    string m_type;                   // 註冊器類型
    HashMap<Key, RegistryItem<Val>*> m_items; // 存儲註冊項目的哈希表
    int m_maxItems;                  // 最大項目數量
    Key m_lastRegisteredKey;         // 最後註冊的鍵

public:
    // 構造函數
    Registry(const string name = "DefaultRegistry", const string type = "Registry", const int maxItems = 100)
        : m_name(name), m_type(type), m_maxItems(maxItems), m_items(NULL, true)
    {
    }

    // 析構函數
    ~Registry()
    {
        m_items.clear();
    }

    // 註冊新項目（抽象方法，由子類實現）
    virtual RegistryResult<Key>* Register(const string name, const string description, Val value) = 0;

    // 根據ID獲取項目
    RegistryItem<Val>* GetItem(const Key key)
    {
        if(!m_items.contains(key))
        {
            return new RegistryItem<Val>("", "NULL", "NULL", NULL, "NULL");
        }

        return m_items.get(key, NULL);
    }

    // 移除項目
    bool Unregister(const Key key)
    {
        if(!m_items.contains(key))
        {
            return false;
        }

        return m_items.remove(key);
    }

    // 清空註冊器
    void Clear()
    {
        m_items.clear();
        m_lastRegisteredKey = NULL;
    }

    // 獲取註冊器名稱
    string GetName()
    {
        return m_name;
    }

    // 獲取註冊器類型
    string GetType()
    {
        return m_type;
    }

    // 獲取項目數量
    int GetCount()
    {
        return m_items.size();
    }

    // 獲取最大項目數量
    int GetMaxItems()
    {
        return m_maxItems;
    }

    // 獲取所有ID
    int GetAllKeys(Key &keys[])
    {
        Vector<Key>* col = new Vector<Key>(false);
        m_items.keys(col);

        int size = col.size();
        ArrayResize(keys, size);

        for(int i = 0; i < size; i++)
        {
            keys[i] = col.get(i);
        }

        return size;
    }

    // 獲取最後註冊的鍵
    Key GetLastRegisteredKey()
    {
        return m_lastRegisteredKey;
    }

    // 檢查是否已註冊
    bool HasRegistered(const Key key)
    {
        return m_items.contains(key);
    }
};
