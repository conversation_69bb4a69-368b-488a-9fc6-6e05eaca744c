//+------------------------------------------------------------------+
//|                                                 RegistryItem.mqh |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| 註冊項目接口 - 只包含讀取方法                                  |
//+------------------------------------------------------------------+
template<typename T>
interface IRegistryItem
{
    // 獲取項目ID
    string GetId() const;

    // 獲取項目名稱
    string GetName() const;

    // 獲取項目描述
    string GetDescription() const;

    // 獲取項目值
    T GetValue() const;

    // 獲取創建時間
    datetime GetCreateTime() const;

    // 獲取最後更新時間
    datetime GetUpdateTime() const;

    // 轉換為字符串（用於調試）
    string ToString() const;
};

//+------------------------------------------------------------------+
//| 註冊項目基本結構 - 實現接口                                      |
//+------------------------------------------------------------------+
template<typename T>
class RegistryItem : public IRegistryItem<T>
{
private:
    string m_id;           // 項目唯一標識符
    string m_name;         // 項目名稱
    string m_description;  // 項目描述
    T      m_value;        // 項目值
    datetime m_createTime; // 創建時間
    datetime m_updateTime; // 最後更新時間

public:
    // 構造函數
    RegistryItem(const string id, const string name, const string description, T value)
        : m_id(id), m_name(name), m_description(description), m_value(value)
    {
        m_createTime = TimeCurrent();
        m_updateTime = m_createTime;
    }

    // 析構函數
    ~RegistryItem() {}

    // 獲取項目ID
    string GetId() const { return m_id; }

    // 獲取項目名稱
    string GetName() const { return m_name; }

    // 設置項目名稱
    void SetName(const string name)
    {
        m_name = name;
        m_updateTime = TimeCurrent();
    }

    // 獲取項目描述
    string GetDescription() const { return m_description; }

    // 設置項目描述
    void SetDescription(const string description)
    {
        m_description = description;
        m_updateTime = TimeCurrent();
    }

    // 獲取項目值
    T GetValue() const { return m_value; }

    // 設置項目值
    void SetValue(T value)
    {
        m_value = value;
        m_updateTime = TimeCurrent();
    }

    // 獲取創建時間
    datetime GetCreateTime() const { return m_createTime; }

    // 獲取最後更新時間
    datetime GetUpdateTime() const { return m_updateTime; }

    // 轉換為字符串（用於調試）
    string ToString() const
    {
        return StringFormat("ID: %s, Name: %s, Description: %s, CreateTime: %s, UpdateTime: %s",
                           m_id, m_name, m_description,
                           TimeToString(m_createTime), TimeToString(m_updateTime));
    }
};
