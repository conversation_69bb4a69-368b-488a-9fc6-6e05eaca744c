#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/mql4-lib-master/Trade/Account.mqh"

// 交易環境檢查階段
class TradingEnvironmentCheckStage : public OnInitPipeline
{
public:
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 檢查交易是否允許
        if(!IsTradeAllowed())
        {
            Print("交易不允許");
            return INIT_FAILED;
        }
        
        // 檢查是否連接到交易伺服器
        if(!IsConnected())
        {
            Print("未連接到交易伺服器");
            return INIT_FAILED;
        }
        
        // 檢查賬戶是否允許交易
        if(!Account::allowsTrade())
        {
            Print("賬戶不允許交易");
            return INIT_FAILED;
        }
        
        // 檢查賬戶是否允許EA交易
        if(!Account::allowsExpertTrade())
        {
            Print("賬戶不允許EA交易");
            return INIT_FAILED;
        }
        
        // 檢查賬戶餘額是否足夠
        if(AccountBalance() < 100)
        {
            Print("賬戶餘額不足: ", AccountBalance());
            return INIT_FAILED;
        }
        
        // 檢查保證金水平是否足夠
        double margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
        if(margin_level < 100.0)
        {
            Print("保證金水平過低: ", margin_level, "%");
            return INIT_FAILED;
        }
        
        // 檢查交易品種是否可交易
        string symbol = Symbol();
        if(MarketInfo(symbol, MODE_TRADEALLOWED) == 0)
        {
            Print("交易品種不可交易: ", symbol);
            return INIT_FAILED;
        }
        
        Print("交易環境檢查通過");
        return INIT_SUCCEEDED;
    }
};
