# Registry 模組 UML 圖表

## 類別圖 (Class Diagram)

```mermaid
classDiagram
    %% 基本介面和類別
    class IRegistryItem~T~ {
        <<interface>>
        +GetId() string
        +GetName() string
        +GetDescription() string
        +GetValue() T
        +GetCreateTime() datetime
        +GetUpdateTime() datetime
        +ToString() string
    }

    class RegistryItem~T~ {
        -m_id: string
        -m_name: string
        -m_description: string
        -m_value: T
        -m_createTime: datetime
        -m_updateTime: datetime
        +RegistryItem(id, name, description, value)
        +GetId() string
        +GetName() string
        +GetDescription() string
        +GetValue() T
        +SetValue(value) void
        +GetCreateTime() datetime
        +GetUpdateTime() datetime
        +ToString() string
    }

    %% 註冊器基類
    class Registry~Key,Val~ {
        <<abstract>>
        #m_name: string
        #m_items: HashMap~Key,IRegistryItem~Val~*~
        #m_maxItems: int
        #m_lastRegisteredKey: Key
        +Registry(name, maxItems)
        +~Registry()
        +Register(name, description, value, key)* bool
        +GenerateKey()* Key
        +KeyToString(id)* string
        +GetItem(id) IRegistryItem~Val~*
        +GetValue(id, defaultValue) Val
        +UpdateValue(id, value) bool
        +Unregister(id) bool
        +Clear() void
        +GetCount() int
        +GetName() string
        +SetName(name) void
        +GetMaxItems() int
        +SetMaxItems(maxItems) void
        +GetAllIds(ids[]) int
        +GetLastRegisteredKey() Key
        +ForEach(callback) void
        +RegisterToRegistry(registry, name, description, value, key) bool
    }

    %% 具體註冊器實現
    class StringRegistry~Val~ {
        #m_keyPrefix: string
        +StringRegistry(name, maxItems)
        +~StringRegistry()
        +GenerateKey() string
        +SetKeyPrefix(prefix) void
        +GetKeyPrefix() string
        +KeyToString(id) string
        +Register(name, description, value, key) bool
    }

    class IntRegistry~Val~ {
        #m_keyPrefix: int
        +IntRegistry(name, maxItems)
        +~IntRegistry()
        +GenerateKey() int
        +SetKeyPrefix(prefix) void
        +GetKeyPrefix() int
        +KeyToString(id) string
        +Register(name, description, value, key) bool
    }

    class PointerRegistry~Val~ {
        #m_lastGeneratedKey: Val
        +PointerRegistry(name, maxItems)
        +~PointerRegistry()
        +GenerateKey() void*
        +KeyToString(id) string
        +Register(name, description, value, key) bool
    }

    %% 特殊註冊器實現
    class StageRegistry {
        -static s_instance: StageRegistry*
        -static s_isActivated: bool
        -StageRegistry()
        -GetInstanceInternal() StageRegistry*
        +GetInstance() StageRegistry*
        +GetInstance(name, maxItems) StageRegistry*
        +~StageRegistry()
        +InitStatic() void
        +RegisterStage(stageName, stageDescription, stageValue, key) bool
        +GetStage(stageId, defaultValue) void*
        +HasStage(stageId) bool
        +UnregisterStage(stageId) bool
        +IsActivated() bool
    }

    class StageManagerRegistry~Val~ {
        -static s_instance: StageManagerRegistry~Val~*
        -static s_isActivated: bool
        -StageManagerRegistry()
        -GetInstanceInternal() StageManagerRegistry~Val~*
        +GetInstance() StageManagerRegistry~Val~*
        +GetInstance(name, maxItems) StageManagerRegistry~Val~*
        +~StageManagerRegistry()
        +InitStatic() void
        +RegisterManager(managerName, managerDescription, managerValue, key) bool
        +GetManager(managerId, defaultValue) Val
        +HasManager(managerId) bool
        +UnregisterManager(managerId) bool
        +IsActivated() bool
    }

    %% 關聯關係
    IRegistryItem <|.. RegistryItem : implements
    Registry <|-- StringRegistry : extends
    Registry <|-- IntRegistry : extends
    Registry <|-- PointerRegistry : extends
    StringRegistry <|-- StageRegistry : extends
    StringRegistry <|-- StageManagerRegistry : extends
    Registry o-- RegistryItem : contains
```

## 流程圖 (Flow Chart)

### 註冊流程

```mermaid
flowchart TD
    A[開始] --> B{檢查是否達到最大項目數量}
    B -->|是| C[註冊失敗]
    B -->|否| D{是否提供了鍵?}
    D -->|是| E{鍵是否已存在?}
    D -->|否| F[生成唯一鍵]
    E -->|是| G[註冊失敗]
    E -->|否| H[創建新項目]
    F --> H
    H --> I[添加到哈希表]
    I --> J[保存最後註冊的鍵]
    J --> K[註冊成功]
    C --> L[結束]
    G --> L
    K --> L
```

### 查詢流程

```mermaid
flowchart TD
    A[開始] --> B{檢查鍵是否存在}
    B -->|是| C[獲取項目]
    B -->|否| D[返回默認值]
    C --> E[返回項目值]
    E --> F[結束]
    D --> F
```

### 更新流程

```mermaid
flowchart TD
    A[開始] --> B{檢查鍵是否存在}
    B -->|是| C[獲取項目]
    B -->|否| D[更新失敗]
    C --> E[設置新值]
    E --> F[更新時間戳]
    F --> G[更新成功]
    D --> H[結束]
    G --> H
```

### 刪除流程

```mermaid
flowchart TD
    A[開始] --> B{檢查鍵是否存在}
    B -->|是| C[從哈希表中移除]
    B -->|否| D[刪除失敗]
    C --> E[釋放項目內存]
    E --> F[刪除成功]
    D --> G[結束]
    F --> G
```

## 時序圖 (Sequence Diagram)

### 註冊和使用項目

```mermaid
sequenceDiagram
    participant Client
    participant Registry
    participant RegistryItem
    participant HashMap

    Client->>Registry: GetInstance("StringRegistry")
    Registry-->>Client: registry instance
    
    Client->>Registry: Register("Symbol", "交易品種", "EURUSD")
    Registry->>Registry: GenerateKey()
    Registry->>RegistryItem: new RegistryItem(id, name, description, value)
    RegistryItem-->>Registry: item instance
    Registry->>HashMap: set(id, item)
    Registry-->>Client: id
    
    Client->>Registry: GetValue(id)
    Registry->>HashMap: get(id)
    HashMap-->>Registry: item
    Registry->>RegistryItem: GetValue()
    RegistryItem-->>Registry: value
    Registry-->>Client: value
    
    Client->>Registry: UpdateValue(id, "GBPUSD")
    Registry->>HashMap: get(id)
    HashMap-->>Registry: item
    Registry->>RegistryItem: SetValue("GBPUSD")
    Registry-->>Client: true
    
    Client->>Registry: Unregister(id)
    Registry->>HashMap: remove(id)
    Registry->>RegistryItem: delete item
    Registry-->>Client: true
```

### 使用 StageRegistry 註冊階段

```mermaid
sequenceDiagram
    participant Client
    participant StageRegistry
    participant StringRegistry
    participant RegistryItem
    
    Client->>StageRegistry: GetInstance()
    alt instance is null
        StageRegistry->>StageRegistry: new StageRegistry()
        StageRegistry->>StringRegistry: SetKeyPrefix("SR")
    end
    StageRegistry-->>Client: stageRegistry instance
    
    Client->>StageRegistry: RegisterStage("MarketDataStage", "市場數據更新階段", stageInstance)
    StageRegistry->>StringRegistry: Register("MarketDataStage", "市場數據更新階段", stageInstance)
    StringRegistry->>StringRegistry: GenerateKey()
    StringRegistry->>RegistryItem: new RegistryItem(id, name, description, value)
    StringRegistry-->>StageRegistry: true
    StageRegistry-->>Client: true
    
    Client->>StageRegistry: GetStage(stageId)
    StageRegistry->>StringRegistry: GetValue(stageId)
    StringRegistry-->>StageRegistry: stageInstance
    StageRegistry-->>Client: stageInstance
```
