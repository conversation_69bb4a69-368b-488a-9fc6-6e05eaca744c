#property strict

#include "../Pipeline/Pipeline.mqh"
#include "ValidationGroup.mqh"

//+------------------------------------------------------------------+
//| 驗證流水線階段                                                      |
//| 用於在流水線中執行驗證                                               |
//+------------------------------------------------------------------+
class CValidationPipeline : public IPipeline<bool, void*>
{
private:
    CValidationGroup* m_validationGroup;  // 驗證群組
    string m_name;                        // 階段名稱
    bool m_continueOnError;               // 驗證失敗時是否繼續流水線

public:
    // 建構函數
    CValidationPipeline(const string name = "ValidationPipeline")
        : m_name(name)
        , m_continueOnError(false)
    {
        m_validationGroup = new CValidationGroup(name);
    }
    
    // 解構函數
    ~CValidationPipeline()
    {
        if(m_validationGroup != NULL)
        {
            delete m_validationGroup;
            m_validationGroup = NULL;
        }
    }
    
    // 執行驗證
    virtual bool Execute(void* in = NULL) override
    {
        Print("執行驗證階段: ", m_name);
        
        CValidationResult* result = m_validationGroup.ValidateAll();
        bool isValid = result.IsValid();
        
        if(!isValid)
        {
            Print("驗證失敗: ", result.GetMessage());
            
            // 如果設置為驗證失敗時繼續流水線，則返回 true
            if(m_continueOnError)
            {
                Print("警告: 驗證失敗，但繼續執行流水線");
                delete result;
                return true;
            }
        }
        else
        {
            Print("驗證成功");
        }
        
        delete result;
        return isValid;
    }
    
    // 獲取驗證群組
    CValidationGroup* GetValidationGroup()
    {
        return m_validationGroup;
    }
    
    // 設置階段名稱
    void SetName(const string name)
    {
        m_name = name;
        if(m_validationGroup != NULL)
        {
            m_validationGroup.SetName(name);
        }
    }
    
    // 獲取階段名稱
    string GetName() const
    {
        return m_name;
    }
    
    // 設置驗證失敗時是否繼續流水線
    void SetContinueOnError(const bool continueOnError)
    {
        m_continueOnError = continueOnError;
    }
    
    // 獲取驗證失敗時是否繼續流水線
    bool GetContinueOnError() const
    {
        return m_continueOnError;
    }
    
    // 添加驗證器
    bool AddValidator(IValidator* validator)
    {
        if(m_validationGroup == NULL)
        {
            return false;
        }
        
        return m_validationGroup.AddValidator(validator);
    }
};
