# EA_Wizard 階段模組

EA_Wizard 階段模組提供了一套完整的 EA 階段管理框架，用於實現 EA 的初始化、周期性執行和終止階段。

## 模組組件

- `OnInitStage.mqh` - EA 初始化階段相關類
- `OnTickStage.mqh` - EA 周期性執行階段相關類
- `OnDeinitStage.mqh` - EA 終止階段相關類

## 階段類型

### 初始化階段 (OnInitStage)

初始化階段在 EA 加載時執行，用於初始化 EA 的各種參數和資源。

常見的初始化階段包括：

1. **OnInit 開始階段**
   - 檢查是否連接到交易伺服器
   - 檢查交易是否允許

2. **參數讀取階段**
   - 讀取交易參數（魔術數字、交易量、滑點、止損點數、止盈點數）
   - 讀取風險管理參數（最大風險百分比、最大持倉數量）
   - 讀取指標參數（移動平均線週期、RSI 週期、RSI 超買超賣水平）
   - 驗證參數是否有效

3. **變數初始化階段**
   - 獲取交易品種信息（點值、最小交易量、最大交易量等）
   - 初始化訂單管理器
   - 將交易品種信息和訂單管理器註冊到註冊器

4. **交易環境檢查階段**
   - 檢查交易是否允許
   - 檢查是否連接到交易伺服器
   - 檢查賬戶是否允許交易
   - 檢查賬戶是否允許 EA 交易
   - 檢查賬戶餘額是否足夠
   - 檢查保證金水平是否足夠
   - 檢查交易品種是否可交易

5. **指標初始化階段**
   - 初始化移動平均線指標
   - 初始化 RSI 指標
   - 初始化 MACD 指標
   - 將指標句柄註冊到註冊器

6. **OnInit 結束階段**
   - 顯示初始化成功信息
   - 設置圖表屬性

### 周期性執行階段 (OnTickStage)

周期性執行階段在每個報價更新時執行，用於實現 EA 的交易邏輯。

常見的周期性執行階段包括：

1. **OnTick 開始階段**
   - 檢查交易是否允許
   - 檢查是否連接到交易伺服器

2. **市場數據更新階段**
   - 更新市場報價
   - 檢查市場數據有效性

3. **持倉狀態檢查階段**
   - 掃描當前訂單
   - 統計買入和賣出訂單數量及交易量
   - 檢查是否有持倉

4. **信號檢查階段**
   - 檢查是否有交易信號
   - 計算信號強度

5. **風險控制檢查階段**
   - 檢查帳戶餘額和權益
   - 檢查保證金水平
   - 檢查持倉數量
   - 檢查風險百分比

6. **交易執行階段**
   - 根據信號執行交易
   - 設置止損和止盈

7. **日誌記錄和通知階段**
   - 記錄交易信息
   - 發送警報和通知

8. **OnTick 結束階段**
   - 記錄執行時間
   - 完成 OnTick 處理

### 終止階段 (OnDeinitStage)

終止階段在 EA 卸載時執行，用於清理 EA 使用的資源。

常見的終止階段包括：

1. **清理階段**
   - 清理註冊器中的所有項目
   - 刪除所有對象

2. **日誌記錄階段**
   - 記錄 EA 停止的原因
   - 記錄 EA 運行的統計信息

## 使用方法

### 初始化階段

```mq4
// 創建初始化階段處理器
OnInitStageManager* manager = OnInitStageManager::GetInstance();

// 添加階段
manager.AddPipeline(new OnInitStartStage());
manager.AddPipeline(new ParameterReadStage());
manager.AddPipeline(new VariableInitStage());
manager.AddPipeline(new TradingEnvironmentCheckStage());
manager.AddPipeline(new IndicatorInitStage());
manager.AddPipeline(new OnInitEndStage());

// 執行階段
ENUM_INIT_RETCODE result = manager.Execute();

// 處理結果
if(result != INIT_SUCCEEDED) {
    Print("初始化失敗，代碼: ", result);
    return result;
}

Print("初始化成功");
return INIT_SUCCEEDED;
```

### 周期性執行階段

```mq4
// 創建周期性執行階段處理器
OnTickStageManager* manager = OnTickStageManager::GetInstance();

// 添加階段
manager.AddPipeline(new OnTickStartStage());
manager.AddPipeline(new MarketDataStage());
manager.AddPipeline(new PositionCheckStage());
manager.AddPipeline(new SignalCheckStage());
manager.AddPipeline(new RiskCheckStage());
manager.AddPipeline(new TradeExecutionStage());
manager.AddPipeline(new LoggingStage());
manager.AddPipeline(new OnTickEndStage());

// 執行階段
bool result = manager.Execute();

// 處理結果
if(!result) {
    Print("周期性執行失敗");
    return;
}

Print("周期性執行成功");
```

### 終止階段

```mq4
// 創建終止階段處理器
OnDeinitStageManager* manager = OnDeinitStageManager::GetInstance();

// 添加階段
manager.AddPipeline(new CleanupStage());
manager.AddPipeline(new LoggingStage());

// 執行階段
bool result = manager.Execute(reason);

// 處理結果
if(!result) {
    Print("終止失敗");
    return;
}

Print("終止成功");
```
