# EA_Wizard 初始化流水線階段

以下是已實現的初始化流水線階段：

1. **OnInit 開始 (OnInitStartStage)**

   - 檢查是否連接到交易伺服器
   - 檢查交易是否允許

2. **參數讀取 (ParameterReadStage)**

   - 讀取交易參數（魔術數字、交易量、滑點、止損點數、止盈點數）
   - 讀取風險管理參數（最大風險百分比、最大持倉數量）
   - 讀取指標參數（移動平均線週期、RSI 週期、RSI 超買超賣水平）
   - 驗證參數是否有效

3. **變數初始化 (VariableInitStage)**

   - 獲取交易品種信息（點值、最小交易量、最大交易量等）
   - 初始化訂單管理器
   - 將交易品種信息和訂單管理器註冊到註冊器

4. **交易環境檢查 (TradingEnvironmentCheckStage)**

   - 檢查交易是否允許
   - 檢查是否連接到交易伺服器
   - 檢查賬戶是否允許交易
   - 檢查賬戶是否允許 EA 交易
   - 檢查賬戶餘額是否足夠
   - 檢查保證金水平是否足夠
   - 檢查交易品種是否可交易

5. **指標初始化 (IndicatorInitStage)**

   - 初始化移動平均線指標
   - 初始化 RSI 指標
   - 初始化 MACD 指標
   - 將指標句柄註冊到註冊器

6. **OnInit 結束 (OnInitEndStage)**

   - 顯示初始化成功信息
   - 設置圖表屬性

## 使用方法

```mq4
// 創建初始化流水線處理器
InitPipelineProcessor* processor = new InitPipelineProcessor(Symbol());

// 執行初始化流水線
ENUM_INIT_RETCODE result = processor.Execute();

// 處理初始化結果
switch (result)
{
case INIT_PARAMETERS_INCORRECT:
   Print("EA初始化失敗，原因: 參數錯誤");
   delete processor;
   return(result);
case INIT_FAILED:
   Print("EA初始化失敗，原因: 初始化失敗");
   delete processor;
   return(result);
}

Print("EA初始化成功");
delete processor;
return(INIT_SUCCEEDED);
```

## 流程圖

```
EA加載/啟動
    |
    v
OnInit 開始
    |
    v
讀取/設定參數
    |
    v
初始化變數
    |
    v
檢查交易環境
    |
    v
環境檢查通過?
   / \
  /   \
 否    是
  |     |
  v     v
顯示錯誤信息  載入/初始化指標
停止EA      |
         v
      初始化完成
```

## 擴展方法

要添加新的初始化階段，請按照以下步驟操作：

1. 創建一個新的階段類，繼承自 OnInitPipeline
2. 實現 Execute 方法，返回 ENUM_INIT_RETCODE
3. 在 InitPipelineProcessor 的構造函數中添加新的階段

例如：

```mq4
// 創建新的初始化階段
class InitNewStage : public OnInitPipeline
{
public:
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 實現初始化邏輯
        return INIT_SUCCEEDED;
    }
};

// 在 InitPipelineProcessor 中添加新階段
InitPipelineProcessor(string symbol = NULL) {
    // 設置註冊器的鍵字頭
    GetRegistry().SetKeyPrefix("Init");

    // 交易品種
    string trade_symbol = (symbol == NULL) ? Symbol() : symbol;

    // 添加初始化階段
    AddPipeline(new OnInitStartStage());
    AddPipeline(new ParameterReadStage());
    AddPipeline(new VariableInitStage(trade_symbol));
    AddPipeline(new TradingEnvironmentCheckStage());
    AddPipeline(new IndicatorInitStage(trade_symbol));
    AddPipeline(new OnInitEndStage());
    AddPipeline(new InitNewStage()); // 添加新階段
}
```
