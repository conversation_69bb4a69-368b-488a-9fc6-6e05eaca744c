//+------------------------------------------------------------------+
//|                                             RegistryExample.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "Registry.mqh"

//+------------------------------------------------------------------+
//| 示例類 - 用於演示註冊器的使用                                     |
//+------------------------------------------------------------------+
class RegistryExample
{
public:
    // 演示如何使用註冊器註冊和管理字符串
    static void DemoStringRegistry()
    {
        // 獲取字符串註冊器實例
        Registry<string, string>* stringRegistry = Registry<string, string>::GetInstance("StringRegistry");

        // 註冊幾個字符串
        string id1 = stringRegistry.Register("Symbol", "交易品種", "EURUSD");
        string id2 = stringRegistry.Register("TimeFrame", "時間週期", "H1");
        string id3 = stringRegistry.Register("Strategy", "策略名稱", "MA Cross");

        // 獲取註冊的值
        string symbol = stringRegistry.GetValue(id1);
        Print("註冊的交易品種: ", symbol);

        // 更新值
        stringRegistry.UpdateValue(id1, "GBPUSD");
        symbol = stringRegistry.GetValue(id1);
        Print("更新後的交易品種: ", symbol);

        // 獲取所有註冊的ID
        string ids[];
        stringRegistry.GetAllIds(ids);
        Print("註冊的項目數量: ", ArraySize(ids));

        // 遍歷所有項目
        for(int i = 0; i < ArraySize(ids); i++)
        {
            RegistryItem<string>* item = stringRegistry.GetItem(ids[i]);
            if(item != NULL)
            {
                Print(item.ToString());
            }
        }

        // 使用回調函數遍歷
        stringRegistry.ForEach(PrintStringItem);

        // 移除項目
        stringRegistry.Unregister(id3);
        Print("移除後的項目數量: ", stringRegistry.GetCount());
    }

    // 演示如何使用註冊器註冊和管理對象指針
    static void DemoObjectRegistry()
    {
        // 獲取對象註冊器實例
        Registry<string, CObject*>* objectRegistry = Registry<string, CObject*>::GetInstance("ObjectRegistry");

        // 創建並註冊一些對象
        CArrayString* array1 = new CArrayString();
        array1.Add("Item1");
        array1.Add("Item2");

        CArrayInt* array2 = new CArrayInt();
        array2.Add(100);
        array2.Add(200);

        string id1 = objectRegistry.Register("StringArray", "字符串數組", array1);
        string id2 = objectRegistry.Register("IntArray", "整數數組", array2);

        // 獲取註冊的對象
        CArrayString* retrievedArray = dynamic_cast<CArrayString*>(objectRegistry.GetValue(id1));
        if(retrievedArray != NULL)
        {
            Print("註冊的字符串數組第一項: ", retrievedArray.At(0));
        }

        // 清空註冊器（會自動刪除所有註冊的對象）
        objectRegistry.Clear();
        Print("清空後的項目數量: ", objectRegistry.GetCount());
    }

private:
    // 用於遍歷的回調函數
    static void PrintStringItem(const string id, RegistryItem<string>* item)
    {
        if(item != NULL)
        {
            Print("ID: ", id, ", 名稱: ", item.GetName(), ", 值: ", item.GetValue());
        }
    }
};
