#property strict

#include "../EACompoundPipeline.mqh"
#include "../decorator/EALogCompoundPipeline.mqh"
#include "../decorator/EAErrorHandlingCompoundPipeline.mqh"

class EACompoundPipelineFactory
{
public:
    EACompoundPipelineFactory()
    {
    }

    EACompoundPipeline* CreateCompundPipeline(string name, string type = "EACompoundPipeline", int maxItems = 100)
    {
        return new EACompoundPipeline(name, type, maxItems);
    }
};

class EACompoundPipelineDecoratorFactory
{
public:
    EACompoundPipelineDecoratorFactory()
    {
    }

    virtual EACompoundPipeline* CreateCompundPipeline(EACompoundPipeline* composite, string type = "EACompoundPipeline") = 0;
};

class EALogCompoundPipelineFactory : public EACompoundPipelineDecoratorFactory
{
private:
    EAFileLog* m_logger;

public:
    EALogCompoundPipelineFactory(EAFileLog* file_log = NULL)
    : m_logger(file_log?file_log:EAFileLog::GetInstance())
    {
    }

    EACompoundPipeline* CreateCompundPipeline(EACompoundPipeline* composite, string type = "EACompoundPipeline") override
    {
        return new EALogCompoundPipeline(composite, type, m_logger);
    }
};

class EAErrorHandlingCompoundPipelineFactory : public EACompoundPipelineDecoratorFactory
{
private:
    EAErrorHandler* m_error_handler;

public:
    EAErrorHandlingCompoundPipelineFactory(EAErrorHandler* error_handler = NULL)
    : m_error_handler(error_handler?error_handler:EAErrorHandler::GetInstance())
    {
    }

    EACompoundPipeline* CreateCompundPipeline(EACompoundPipeline* composite, string type = "EACompoundPipeline") override
    {
        return new EAErrorHandlingCompoundPipeline(composite, type, m_error_handler);
    }
};
