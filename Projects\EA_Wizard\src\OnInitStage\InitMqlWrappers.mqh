#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/Trade/MqlStruct.mqh"
#include "../../module/Util/ErrorHandler.mqh"

// 初始化 MQL 封裝器階段
class InitMqlWrapper : public OnInitStage
{
public:
    InitMqlWrapper() : OnInitStage(ONINIT_STAGE_VARIABLE_INIT){}
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化 MQL 封裝器階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            return INIT_FAILED;
        }

        if(!this.IsRegistered()) {
            error_handler.HandleError("MQL 封裝器階段註冊失敗");
            return INIT_FAILED;
        }
        
        // 創建 MQL開倉 封裝器實例
        MqlOrderWrapper* mql_order_wrapper = new MqlOrderWrapper();
        if(mql_order_wrapper == NULL) {
            error_handler.HandleError("無法創建 MQL開倉 封裝器實例");
            return INIT_FAILED;
        }

        // 創建 MQL平倉 封裝器實例
        MqlCloseWrapper* mql_close_wrapper = new MqlCloseWrapper();
        if(mql_close_wrapper == NULL) {
            error_handler.HandleError("無法創建 MQL平倉 封裝器實例");
            delete mql_order_wrapper; // 釋放資源
            return INIT_FAILED;
        }
        
        // 註冊 MQL開倉 封裝器
        if(!Register("MqlOrderWrapper", "MQL開倉 封裝器", mql_order_wrapper, "MqlOrderWrapper"))
        {
            error_handler.HandleError("MQL開倉 封裝器註冊失敗");
            delete mql_order_wrapper; // 釋放資源
            return INIT_FAILED;
        }

        // 註冊 MQL平倉 封裝器
        if(!Register("MqlCloseWrapper", "MQL平倉 封裝器", mql_close_wrapper, "MqlCloseWrapper"))
        {
            error_handler.HandleError("MQL平倉 封裝器註冊失敗");
            delete mql_close_wrapper; // 釋放資源
            return INIT_FAILED;
        }

        Print("MQL 封裝器初始化成功");
        return INIT_SUCCEEDED;
    }
}init_mql_wrapper_stage;
