#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"
#include "SignalCheckStage.mqh"
#include "TradeExecutionStage.mqh"

// 日誌記錄和通知階段
class LoggingStage : public OnTickPipeline
{
private:
    string m_symbol;
    SignalCheckStage* m_signal_stage;
    TradeExecutionStage* m_trade_stage;
    bool m_use_alerts;
    bool m_use_push_notifications;
    bool m_use_email_notifications;
    
public:
    // 建構函數
    LoggingStage(SignalCheckStage* signal_stage, 
                TradeExecutionStage* trade_stage,
                string symbol = NULL,
                bool use_alerts = true,
                bool use_push_notifications = false,
                bool use_email_notifications = false)
        : m_signal_stage(signal_stage),
          m_trade_stage(trade_stage),
          m_symbol(symbol == NULL ? Symbol() : symbol),
          m_use_alerts(use_alerts),
          m_use_push_notifications(use_push_notifications),
          m_use_email_notifications(use_email_notifications)
    {
    }
    
    bool Execute(void* in = NULL) override
    {
        // 檢查信號階段和交易階段是否有效
        if(m_signal_stage == NULL || m_trade_stage == NULL)
        {
            Print("信號檢查階段或交易執行階段未設置");
            return false;
        }
        
        // 獲取信號類型
        int signal_type = m_signal_stage.GetSignalType();
        bool has_signal = m_signal_stage.HasSignal();
        
        // 獲取訂單號
        int ticket = m_trade_stage.GetTicket();
        
        // 記錄信息
        string log_message = "";
        
        if(has_signal && ticket > 0)
        {
            // 如果有信號且交易成功
            string signal_str = (signal_type == SIGNAL_BUY) ? "買入" : "賣出";
            log_message = "EA_Wizard 交易執行成功 - 交易品種: " + m_symbol + 
                          ", 信號類型: " + signal_str + 
                          ", 訂單號: " + IntegerToString(ticket);
            
            // 記錄到日誌
            Print(log_message);
            
            // 發送警報
            if(m_use_alerts)
            {
                Alert(log_message);
            }
            
            // 發送推送通知
            if(m_use_push_notifications)
            {
                SendNotification(log_message);
            }
            
            // 發送電子郵件通知
            if(m_use_email_notifications)
            {
                SendMail("EA_Wizard 交易通知", log_message);
            }
        }
        else if(has_signal && ticket <= 0)
        {
            // 如果有信號但交易失敗
            string signal_str = (signal_type == SIGNAL_BUY) ? "買入" : "賣出";
            log_message = "EA_Wizard 交易執行失敗 - 交易品種: " + m_symbol + 
                          ", 信號類型: " + signal_str;
            
            // 記錄到日誌
            Print(log_message);
            
            // 發送警報
            if(m_use_alerts)
            {
                Alert(log_message);
            }
        }
        else
        {
            // 如果沒有信號
            log_message = "EA_Wizard 無交易信號 - 交易品種: " + m_symbol;
            
            // 記錄到日誌
            Print(log_message);
        }
        
        return true;
    }
};
