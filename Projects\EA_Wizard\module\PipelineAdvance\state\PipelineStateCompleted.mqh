//+------------------------------------------------------------------+
//|                                     PipelineStateCompleted.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "PipelineStateBase.mqh"
#include "../state/PipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已完成狀態 - 實現已完成狀態的行為                           |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStateCompleted : public PipelineStateBase<PipelineObj>
{
public:
    // 構造函數
    PipelineStateCompleted()
    : PipelineStateBase(PIPELINE_STATE_COMPLETED, "已完成") {}
    
    // 析構函數
    ~PipelineStateCompleted() {}
    
    // 執行流水線
    void Execute(PipelineObj pipeline) override;
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline) override;
};

template <typename PipelineObj>
void PipelineStateCompleted::Execute(PipelineObj pipeline)
{
    // 已完成狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經完成執行，不能重複執行");
}

template <typename PipelineObj>
void PipelineStateCompleted::Restore(PipelineObj pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new PipelineStatePending<PipelineObj>());
}
