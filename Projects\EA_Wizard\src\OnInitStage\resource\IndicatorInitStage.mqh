#property strict

#include "../../module/Pipeline/OnInitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"

// 指標初始化階段
class IndicatorInitStage : public OnInitPipeline
{
private:
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;
    
public:
    // 建構函數
    IndicatorInitStage(string symbol = NULL, ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT)
        : m_symbol(symbol == NULL ? Symbol() : symbol),
          m_timeframe(timeframe == PERIOD_CURRENT ? Period() : timeframe)
    {
    }
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override
    {
        // 獲取註冊器
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        
        // 從註冊器獲取指標參數
        int ma_fast_period = 10;
        int ma_slow_period = 20;
        ENUM_MA_METHOD ma_method = MODE_SMA;
        int rsi_period = 14;
        double rsi_overbought = 70.0;
        double rsi_oversold = 30.0;
        
        void* ma_fast_ptr = NULL;
        if(registry.Get("MAFastPeriod", ma_fast_ptr))
        {
            ma_fast_period = *(int*)ma_fast_ptr;
        }
        
        void* ma_slow_ptr = NULL;
        if(registry.Get("MASlowPeriod", ma_slow_ptr))
        {
            ma_slow_period = *(int*)ma_slow_ptr;
        }
        
        void* ma_method_ptr = NULL;
        if(registry.Get("MAMethod", ma_method_ptr))
        {
            ma_method = *(ENUM_MA_METHOD*)ma_method_ptr;
        }
        
        void* rsi_period_ptr = NULL;
        if(registry.Get("RSIPeriod", rsi_period_ptr))
        {
            rsi_period = *(int*)rsi_period_ptr;
        }
        
        void* rsi_overbought_ptr = NULL;
        if(registry.Get("RSIOverbought", rsi_overbought_ptr))
        {
            rsi_overbought = *(double*)rsi_overbought_ptr;
        }
        
        void* rsi_oversold_ptr = NULL;
        if(registry.Get("RSIOversold", rsi_oversold_ptr))
        {
            rsi_oversold = *(double*)rsi_oversold_ptr;
        }
        
        // 初始化移動平均線指標
        int ma_fast_handle = iMA(m_symbol, m_timeframe, ma_fast_period, 0, ma_method, PRICE_CLOSE);
        int ma_slow_handle = iMA(m_symbol, m_timeframe, ma_slow_period, 0, ma_method, PRICE_CLOSE);
        
        // 檢查指標是否初始化成功
        if(ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE)
        {
            Print("移動平均線指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 初始化 RSI 指標
        int rsi_handle = iRSI(m_symbol, m_timeframe, rsi_period, PRICE_CLOSE);
        
        if(rsi_handle == INVALID_HANDLE)
        {
            Print("RSI 指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 初始化 MACD 指標
        int macd_fast_ema = 12;
        int macd_slow_ema = 26;
        int macd_signal = 9;
        int macd_handle = iMACD(m_symbol, m_timeframe, macd_fast_ema, macd_slow_ema, macd_signal, PRICE_CLOSE);
        
        if(macd_handle == INVALID_HANDLE)
        {
            Print("MACD 指標初始化失敗");
            return INIT_FAILED;
        }
        
        // 將指標句柄註冊到註冊器
        registry.Register("MAFastHandle", "快速移動平均線句柄", &ma_fast_handle);
        registry.Register("MASlowHandle", "慢速移動平均線句柄", &ma_slow_handle);
        registry.Register("RSIHandle", "RSI句柄", &rsi_handle);
        registry.Register("MACDHandle", "MACD句柄", &macd_handle);
        
        // 將RSI超買超賣水平註冊到註冊器
        registry.Register("RSIOverbought", "RSI超買水平", &rsi_overbought);
        registry.Register("RSIOversold", "RSI超賣水平", &rsi_oversold);
        
        Print("指標初始化成功 - 交易品種: ", m_symbol, 
              ", 時間週期: ", EnumToString(m_timeframe), 
              ", 快速MA週期: ", ma_fast_period, 
              ", 慢速MA週期: ", ma_slow_period, 
              ", RSI週期: ", rsi_period);
        
        return INIT_SUCCEEDED;
    }
};
