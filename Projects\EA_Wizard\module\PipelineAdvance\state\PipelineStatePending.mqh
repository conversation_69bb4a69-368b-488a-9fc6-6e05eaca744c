//+------------------------------------------------------------------+
//|                                       PipelineStatePending.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "PipelineStateBase.mqh"
#include "../state/PipelineStateRunning.mqh"

//+------------------------------------------------------------------+
//| 流水線待執行狀態 - 實現待執行狀態的行為                           |
//+------------------------------------------------------------------+
template <typename PipelineObj>
class PipelineStatePending : public PipelineStateBase<PipelineObj>
{
public:
    // 構造函數
    PipelineStatePending()
    : PipelineStateBase(PIPELINE_STATE_PENDING, "待執行") {}
    
    // 析構函數
    ~PipelineStatePending() {}
    
    // 執行流水線
    void Execute(PipelineObj pipeline) override;
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline) override;
};

template <typename PipelineObj>
void PipelineStatePending::Execute(PipelineObj pipeline)
{
    // 將狀態轉換為執行中
    pipeline.SetState(new PipelineStateRunning<PipelineObj>());

    // 執行實際的流水線邏輯
    pipeline.Main();
    
    // 根據執行結果更新狀態
    // 這部分應該在流水線內部實現，而不是在狀態中實現
}

template <typename PipelineObj>
void PipelineStatePending::Restore(PipelineObj pipeline)
{
    // 待執行狀態下的重置不需要做任何事情，因為已經是初始狀態
}
