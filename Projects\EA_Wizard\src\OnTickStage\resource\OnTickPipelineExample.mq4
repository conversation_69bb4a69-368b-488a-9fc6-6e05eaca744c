//+------------------------------------------------------------------+
//|                                         OnTickPipelineExample.mq4 |
//|                                                       EA_Wizard    |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "OnTickStage.mqh"

// 輸入參數
input int MagicNumber = 12345;        // 魔術數字
input double LotSize = 0.01;          // 交易量
input double StopLoss = 20.0;         // 止損點數
input double TakeProfit = 40.0;       // 止盈點數
input double MaxRiskPercent = 2.0;    // 最大風險百分比
input int MaxPositions = 5;           // 最大持倉數量

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // 檢查輸入參數
    if(MagicNumber <= 0)
    {
        Print("魔術數字必須大於0");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    if(LotSize <= 0.0)
    {
        Print("交易量必須大於0");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    if(StopLoss <= 0.0)
    {
        Print("止損點數必須大於0");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    if(TakeProfit <= 0.0)
    {
        Print("止盈點數必須大於0");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    if(MaxRiskPercent <= 0.0 || MaxRiskPercent > 100.0)
    {
        Print("最大風險百分比必須在0-100之間");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    if(MaxPositions <= 0)
    {
        Print("最大持倉數量必須大於0");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    Print("EA初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("EA卸載，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
    // 創建交易流水線處理器
    TickPipelineProcessor* processor = new TickPipelineProcessor(MagicNumber, Symbol());
    
    // 執行流水線
    bool result = processor.Execute();
    
    // 處理結果
    if(!result)
    {
        Print("交易流水線執行失敗");
    }
    else
    {
        Print("交易流水線執行成功");
    }
    
    // 釋放處理器
    delete processor;
}
//+------------------------------------------------------------------+
