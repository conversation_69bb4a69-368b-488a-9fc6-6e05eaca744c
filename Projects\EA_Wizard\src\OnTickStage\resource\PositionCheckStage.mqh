#property strict

#include "../../module/Pipeline/OnTickPipeline.mqh"

// 持倉狀態檢查階段
class PositionCheckStage : public OnTickPipeline
{
private:
    int m_magic_number;
    string m_symbol;
    bool m_has_positions;
    
public:
    // 建構函數
    PositionCheckStage(int magic_number = 0, string symbol = NULL)
        : m_magic_number(magic_number),
          m_symbol(symbol == NULL ? Symbol() : symbol),
          m_has_positions(false)
    {
    }
    
    // 獲取是否有持倉
    bool HasPositions() const
    {
        return m_has_positions;
    }
    
    bool Execute(void* in = NULL) override
    {
        // 掃描當前訂單
        int total_orders = OrdersTotal();
        int open_buy_orders = 0;
        int open_sell_orders = 0;
        double total_buy_volume = 0.0;
        double total_sell_volume = 0.0;
        
        for(int i = 0; i < total_orders; i++)
        {
            if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
                continue;
                
            // 如果指定了魔術數字，則只處理該魔術數字的訂單
            if(m_magic_number > 0 && OrderMagicNumber() != m_magic_number)
                continue;
                
            // 如果指定了交易品種，則只處理該交易品種的訂單
            if(m_symbol != "" && OrderSymbol() != m_symbol)
                continue;
                
            // 統計買入和賣出訂單
            if(OrderType() == OP_BUY)
            {
                open_buy_orders++;
                total_buy_volume += OrderLots();
            }
            else if(OrderType() == OP_SELL)
            {
                open_sell_orders++;
                total_sell_volume += OrderLots();
            }
        }
        
        // 更新持倉狀態
        m_has_positions = (open_buy_orders > 0 || open_sell_orders > 0);
        
        // 輸出持倉狀態
        Print("持倉狀態 - 交易品種: ", m_symbol, 
              ", 魔術數字: ", m_magic_number,
              ", 買入訂單: ", open_buy_orders, 
              ", 賣出訂單: ", open_sell_orders,
              ", 買入總量: ", DoubleToString(total_buy_volume, 2),
              ", 賣出總量: ", DoubleToString(total_sell_volume, 2),
              ", 是否有持倉: ", m_has_positions ? "是" : "否");
        
        return true;
    }
};
