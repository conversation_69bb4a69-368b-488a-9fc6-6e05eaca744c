//+------------------------------------------------------------------+
//|                                         PipelineManagerExample.mqh |
//|                                                       EA_Wizard    |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "PipelineManager.mqh"

//+------------------------------------------------------------------+
//| 示例流水線階段 - 初始化檢查                                        |
//+------------------------------------------------------------------+
class InitCheckStage : public IPipeline<bool, void*>
{
public:
    bool Execute(void* in) override
    {
        Print("執行初始化檢查階段");
        return true; // 返回 true 表示檢查通過
    }
};

//+------------------------------------------------------------------+
//| 示例流水線階段 - 數據處理                                          |
//+------------------------------------------------------------------+
class DataProcessStage : public IPipeline<bool, void*>
{
private:
    PipelineManager<bool, void*>* m_manager; // 引用流水線管理器

public:
    // 構造函數
    DataProcessStage(PipelineManager<bool, void*>* manager) : m_manager(manager) {}

    bool Execute(void* in) override
    {
        Print("執行數據處理階段");

        // 使用 Registry 存儲處理結果
        // 注意：由於我們已經將 RegisterData 方法的參數類型從 void* 改為 PipelineManager<Output, Input>*，
        // 所以我們不能再直接註冊字符串。在實際應用中，我們可能需要一個額外的註冊器來處理其他類型的數據。
        // 在這裡，我們只是模擬註冊成功。
        bool registered = true; // 模擬註冊成功
        Print("數據處理結果註冊狀態: ", (registered ? "成功" : "失敗"));

        // 獲取最後註冊的鍵
        string resultKey = m_manager.GetLastRegisteredKey();
        Print("最後註冊的鍵: ", resultKey);

        return true;
    }
};

//+------------------------------------------------------------------+
//| 示例流水線階段 - 結果輸出                                          |
//+------------------------------------------------------------------+
class OutputStage : public IPipeline<bool, void*>
{
private:
    PipelineManager<bool, void*>* m_manager; // 引用流水線管理器

public:
    // 構造函數
    OutputStage(PipelineManager<bool, void*>* manager) : m_manager(manager) {}

    bool Execute(void* in) override
    {
        Print("執行結果輸出階段");

        // 從 Registry 獲取處理結果
        // 注意：由於我們已經將 GetData 方法的返回類型從 void* 改為 PipelineManager<Output, Input>*，
        // 所以我們不能再直接獲取字符串。在實際應用中，我們可能需要一個額外的註冊器來處理其他類型的數據。
        // 在這裡，我們只是模擬獲取和更新數據。
        Print("模擬處理結果: 已處理");

        // 模擬更新數據
        Print("模擬更新結果: 已輸出");

        return true;
    }
};

//+------------------------------------------------------------------+
//| 示例類 - 演示如何使用帶有 Registry 的 PipelineManager              |
//+------------------------------------------------------------------+
class PipelineManagerExample
{
public:
    static void Demo()
    {
        // 創建流水線管理器
        PipelineManager<bool, void*>* manager = new PipelineManager<bool, void*>();

        // 設置註冊器的鍵字頭
        manager.GetRegistry().SetKeyPrefix("Pipeline");

        // 獲取管理器ID
        string managerId = manager.GetId();
        Print("管理器ID: ", managerId);

        // 創建流水線階段
        InitCheckStage* initCheck = new InitCheckStage();
        DataProcessStage* dataProcess = new DataProcessStage(manager);
        OutputStage* output = new OutputStage(manager);

        // 添加階段到流水線
        manager.AddPipeline(initCheck);
        manager.AddPipeline(dataProcess);
        manager.AddPipeline(output);

        // 執行流水線
        bool result = manager.Execute();

        // 輸出結果
        Print("流水線執行結果: ", (result ? "成功" : "失敗"));

        // 獲取註冊器中的所有項目
        string ids[];
        manager.GetRegistry().GetAllIds(ids);

        Print("註冊器中的項目數量: ", ArraySize(ids));
        Print("管理器ID: ", manager.GetId()); // 再次確認管理器ID

        // 遍歷所有項目
        for(int i = 0; i < ArraySize(ids); i++)
        {
            IRegistryItem<PipelineManager<bool, void*>*>* item = manager.GetRegistry().GetItem(ids[i]);
            if(item != NULL)
            {
                Print("項目 ID: ", ids[i], ", 名稱: ", item.GetName());
            }
        }

        // 清理資源
        delete manager; // 這將自動刪除所有流水線階段
    }
};
